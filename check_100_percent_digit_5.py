import csv
from collections import Counter
import os

def check_100_percent_digit_5():
    """
    檢查是否存在與3,4組合後能讓尾數5機率達到100%的數字
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print('檢查是否存在與3,4組合後100%產生尾數5的數字...')
    print('='*60)
    
    # 存儲所有結果
    results = []
    
    # 檢查所有可能的第三個數字與3,4組合
    for third_number in range(1, 50):
        if third_number == 3 or third_number == 4:
            continue
            
        # 找出包含3, 4, 和 third_number的抽獎
        indices_with_3_4_third = []
        for i, draw in enumerate(draws):
            if 3 in draw and 4 in draw and third_number in draw:
                indices_with_3_4_third.append(i)
        
        if len(indices_with_3_4_third) >= 2:  # 至少2次出現才考慮
            # 獲取下一期的數字
            next_draw_numbers = []
            for idx in indices_with_3_4_third:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers.extend(next_draw)
            
            if next_draw_numbers:
                # 計算尾數分布
                last_digits = [num % 10 for num in next_draw_numbers]
                digit_5_count = last_digits.count(5)
                total_count = len(last_digits)
                
                if total_count > 0:
                    percentage = (digit_5_count / total_count) * 100
                    results.append({
                        'third_number': third_number,
                        'occurrences': len(indices_with_3_4_third),
                        'digit_5_count': digit_5_count,
                        'total_count': total_count,
                        'percentage': percentage
                    })
    
    # 按機率排序
    results.sort(key=lambda x: x['percentage'], reverse=True)
    
    # 找出100%的組合
    perfect_combinations = [r for r in results if r['percentage'] == 100]
    
    print(f"總共找到 {len(perfect_combinations)} 組能讓尾數5機率達到100%的組合")
    print()
    
    if perfect_combinations:
        print("100%尾數5的組合：")
        print("-" * 60)
        for result in perfect_combinations:
            print(f"  數字 3, 4, {result['third_number']}: 100% (尾數5次數: {result['digit_5_count']}/{result['total_count']}) - 出現 {result['occurrences']} 次")
    else:
        print("沒有找到任何能讓尾數5機率達到100%的組合")
        print()
        print("前10名高機率組合：")
        print("-" * 60)
        for i, result in enumerate(results[:10]):
            print(f"  {i+1}. 數字 3, 4, {result['third_number']}: {result['percentage']:.2f}% (尾數5次數: {result['digit_5_count']}/{result['total_count']}) - 出現 {result['occurrences']} 次")
    
    # 也顯示高出現次數的組合
    print()
    print("出現次數最多的組合（至少3次出現）：")
    print("-" * 60)
    high_occurrence_results = [r for r in results if r['occurrences'] >= 3]
    high_occurrence_results.sort(key=lambda x: x['occurrences'], reverse=True)
    for result in high_occurrence_results[:10]:
        print(f"  數字 3, 4, {result['third_number']}: {result['percentage']:.2f}% (尾數5次數: {result['digit_5_count']}/{result['total_count']}) - 出現 {result['occurrences']} 次")

if __name__ == "__main__":
    check_100_percent_digit_5()