import csv
from collections import Counter
import os
from itertools import combinations

def find_all_three_number_must_appear():
    """
    尋找所有三個數字組合出現後，下一期一定會出現的數字
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines1.csv'):
        print("錯誤: 找不到 data_compare_lines1.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("全面分析1-49範圍內所有三個數字組合...")
    print("尋找下一期一定會出現的數字 (至少出現3次才有統計意義)")
    print("="*80)
    
    # 存儲結果
    must_appear_combinations = []
    
    # 生成所有可能的三個數字組合 (1-49)
    count = 0
    total_combinations = 0
    
    # 先計算總組合數
    for _ in combinations(range(1, 50), 3):
        total_combinations += 1
    
    print(f"總共需要檢查 {total_combinations:,} 種組合")
    
    # 由於組合數量龐大，我們分批處理或限制範圍以避免過長的運算時間
    # 這裡我們先檢查出現頻率較高的數字組合
    # 首先統計各個數字的出現頻率
    all_numbers = []
    for draw in draws:
        all_numbers.extend(draw)
    
    number_frequency = Counter(all_numbers)
    most_common_numbers = [num for num, count in number_frequency.most_common(15)]  # 取前15個最常見的數字
    
    print(f"最常出現的數字: {most_common_numbers}")
    print("正在分析最常見數字的組合...")
    
    # 分析最常見數字之間的組合
    analyzed_count = 0
    
    for first_num in most_common_numbers:
        for second_num in most_common_numbers:
            if second_num <= first_num:
                continue
            for third_num in most_common_numbers:
                if third_num <= second_num:
                    continue
                
                # 找出同時包含三個數字的抽獎
                indices_with_triplet = []
                for i, draw in enumerate(draws):
                    if first_num in draw and second_num in draw and third_num in draw:
                        indices_with_triplet.append(i)
                
                # 只考慮至少出現3次的組合
                if len(indices_with_triplet) >= 3:
                    # 獲取下一期的所有數字
                    next_draws_list = []
                    
                    for idx in indices_with_triplet:
                        if idx + 1 < len(draws):
                            next_draw = draws[idx + 1]
                            next_draws_list.append(next_draw)
                    
                    if next_draws_list:
                        # 檢查是否有數字在每一次下一期都出現
                        all_possible_numbers = set()
                        for next_draw in next_draws_list:
                            all_possible_numbers.update(next_draw)
                        
                        numbers_appeared_every_time = []
                        for num in all_possible_numbers:
                            # 計算這個數字在多少期下一期中出現
                            appeared_times = 0
                            for next_draw in next_draws_list:
                                if num in next_draw:
                                    appeared_times += 1
                            
                            # 如果在每一期下一期都出現
                            if appeared_times == len(next_draws_list):
                                numbers_appeared_every_time.append(num)
                        
                        if numbers_appeared_every_time:
                            must_appear_combinations.append({
                                'triplet': (first_num, second_num, third_num),
                                'occurrences': len(indices_with_triplet),
                                'must_appear_numbers': sorted(numbers_appeared_every_time),
                                'next_draws_list': next_draws_list
                            })
                
                analyzed_count += 1
                if analyzed_count % 100 == 0:
                    print(f"已分析 {analyzed_count} 種組合...")
    
    # 輸出結果
    print(f"\n分析完成！總共分析了 {analyzed_count} 種組合")
    
    if must_appear_combinations:
        print(f"\n找到 {len(must_appear_combinations)} 個三個數字組合的模式:")
        print("="*80)
        
        # 按出現次數排序
        must_appear_combinations.sort(key=lambda x: x['occurrences'], reverse=True)
        
        for i, combo in enumerate(must_appear_combinations, 1):
            triplet = combo['triplet']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"{i}. 數字組合 {triplet[0]}, {triplet[1]}, {triplet[2]}:")
            print(f"   出現 {occurrences} 次")
            print(f"   下一期每次都出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            for j, next_draw in enumerate(combo['next_draws_list']):
                print(f"     第{j+1}次後的下一期: {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 60)
    else:
        print("\n沒有找到三個數字組合出現後，下一期每次都出現特定數字的模式")
    
    # 再分析一些出現次數較多的組合
    print("\n擴大範圍：分析出現頻率較高的組合...")
    print("="*80)
    
    # 找出在數據中實際出現的三個數字組合
    actual_triplets = Counter()
    
    for draw in draws:
        if len(draw) >= 3:  # 確保抽獎包含至少3個數字
            for triplet in combinations(sorted(draw), 3):
                actual_triplets[triplet] += 1
    
    # 找出現3次或以上的組合
    frequent_triplets = [triplet for triplet, count in actual_triplets.items() if count >= 3]
    
    print(f"在數據中找到 {len(frequent_triplets)} 個出現3次或以上的三個數字組合")
    
    # 檢查這些組合後是否一定出現特定數字
    actual_must_appear = []
    
    for triplet in frequent_triplets:
        first_num, second_num, third_num = triplet
        
        # 找出包含這個組合的所有抽獎
        indices_with_triplet = []
        for i, draw in enumerate(draws):
            if first_num in draw and second_num in draw and third_num in draw:
                indices_with_triplet.append(i)
        
        # 獲取下一期的數據
        next_draws_list = []
        for idx in indices_with_triplet:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_draws_list.append(next_draw)
        
        if next_draws_list:
            # 檢查是否有數字在每一次下一期都出現
            all_possible_numbers = set()
            for next_draw in next_draws_list:
                all_possible_numbers.update(next_draw)
            
            numbers_appeared_every_time = []
            for num in all_possible_numbers:
                # 計算這個數字在多少期下一期中出現
                appeared_times = 0
                for next_draw in next_draws_list:
                    if num in next_draw:
                        appeared_times += 1
                
                # 如果在每一期下一期都出現
                if appeared_times == len(next_draws_list):
                    numbers_appeared_every_time.append(num)
            
            if numbers_appeared_every_time:
                actual_must_appear.append({
                    'triplet': triplet,
                    'occurrences': len(indices_with_triplet),
                    'must_appear_numbers': sorted(numbers_appeared_every_time),
                    'next_draws_list': next_draws_list
                })
    
    if actual_must_appear:
        print(f"找到 {len(actual_must_appear)} 個實際出現的模式:")
        print("="*80)
        
        # 按出現次數排序
        actual_must_appear.sort(key=lambda x: x['occurrences'], reverse=True)
        
        for i, combo in enumerate(actual_must_appear, 1):
            triplet = combo['triplet']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"{i}. 數字組合 {triplet[0]}, {triplet[1]}, {triplet[2]}:")
            print(f"   出現 {occurrences} 次")
            print(f"   下一期每次都出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            for j, next_draw in enumerate(combo['next_draws_list']):
                print(f"     第{j+1}次後的下一期: {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 60)
    else:
        print("在實際出現的三個數字組合中，也沒有找到下一期一定出現特定數字的模式")

if __name__ == "__main__":
    find_all_three_number_must_appear()