import csv
from collections import Counter

def analyze_number_24_following():
    """
    分析data_compare_lines1.csv中數字24出現後下一期的數字
    """
    # 讀取CSV文件
    with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字24出現的行的索引
    indices_with_24 = []
    for i, draw in enumerate(draws):
        if 24 in draw:
            indices_with_24.append(i)
    
    print(f"數字24在以下期數出現: {[idx+1 for idx in indices_with_24]}")
    print(f"總共出現 {len(indices_with_24)} 次")
    
    # 收集數字24出現後下一期的數字
    following_numbers = []
    for idx in indices_with_24:
        # 檢查是否是最后一期
        if idx + 1 < len(draws):
            following_draw = draws[idx + 1]
            following_numbers.extend(following_draw)
            print(f"第 {idx+1} 期出現24後，第 {idx+2} 期的數字: {following_draw}")
    
    if following_numbers:
        # 統計下一期出現的數字
        number_counts = Counter(following_numbers)
        
        print(f"\n24出現後下一期總共出現 {len(following_numbers)} 個數字")
        print("各數字出現次數統計:")
        for num, count in sorted(number_counts.items()):
            print(f" 數字 {num:2d}: {count} 次")
        
        # 計算平均值
        avg = sum(following_numbers) / len(following_numbers)
        print(f"\n平均值: {avg:.2f}")
        
        # 找出最常出現的數字
        most_common = number_counts.most_common(5)  # 前5名
        print(f"\n最常出現的5個數字:")
        for num, count in most_common:
            print(f" 數字 {num:2d}: {count} 次")
        
        # 分析尾數 (個位數)
        last_digits = [num % 10 for num in following_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\n尾數出現次數統計:")
        for digit in sorted(last_digit_counts.keys()):
            print(f" 尾數 {digit}: {last_digit_counts[digit]} 次")
        
        # 找出最常出現的前三名尾數
        most_common_last_digits = last_digit_counts.most_common(3)  # 前3名
        print(f"\n最常出現的前三名尾數:")
        for digit, count in most_common_last_digits:
            percentage = (count / len(last_digits)) * 100  # 修正百分比計算：乘以100而非10
            print(f"  尾數 {digit}: {count} 次 ({percentage:.2f}%)")
        
        # 分析各尾數包含哪些號碼
        print(f"\n各尾數包含的號碼及其次數:")
        for digit in sorted(set(last_digits)):
            numbers_with_digit = [num for num in following_numbers if num % 10 == digit]
            digit_number_counts = Counter(numbers_with_digit)
            print(f" 尾數 {digit}: {dict(sorted(digit_number_counts.items()))}")
        
        # 分析數字24當期的其他號碼與下一期特定尾數的關係
        print(f"\n分析數字24當期的號碼與下一期特定尾數的關係:")
        
        # 定義目標尾數
        target_digits = [2, 8, 7]  # 前三名尾數
        
        print(f"目標尾數: {target_digits}")
        
        # 為每個目標尾數分析配對號碼
        for target_digit in target_digits:
            print(f"\n=== 分析尾數 {target_digit} 的最佳配對號碼 ===")
            
            # 統計與24同在一期且下期包含目標尾數的號碼
            paired_numbers_stats = {}
            
            for idx in indices_with_24:
                if idx + 1 < len(draws):  # 確保有下一期
                    current_draw = draws[idx]  # 當期號碼（包含24）
                    next_draw = draws[idx + 1]  # 下一期號碼
                    
                    # 檢查當期除了24外的其他號碼
                    other_numbers = [num for num in current_draw if num != 24]
                    
                    # 檢查下一期是否包含目標尾數
                    next_draw_digits = [n % 10 for n in next_draw]
                    has_target_digit = target_digit in next_draw_digits
                    
                    for num in other_numbers:
                        if num not in paired_numbers_stats:
                            paired_numbers_stats[num] = {'total': 0, 'with_target_digit': 0}
                        
                        paired_numbers_stats[num]['total'] += 1
                        
                        if has_target_digit:
                            paired_numbers_stats[num]['with_target_digit'] += 1
            
            # 顯示結果
            print(f"與24同在一期的號碼對應下一期出現尾數{target_digit}的統計:")
            print("(格式: 號碼: [出現次數, 下期有尾數{target_digit}次數, 百分比])")
            
            # 過濾掉總出現次數少於2次的號碼，以確保統計意義
            significant_pairs = {num: stats for num, stats in paired_numbers_stats.items() if stats['total'] >= 2}
            
            if significant_pairs:
                sorted_paired = sorted(significant_pairs.items(), 
                                     key=lambda x: x[1]['with_target_digit']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                     reverse=True)
                
                # 顯示前10名
                for num, stats in sorted_paired[:10]:
                    if stats['total'] > 0:
                        percentage = (stats['with_target_digit'] / stats['total']) * 100
                        print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_target_digit']}, {percentage:.2f}%]")
                
                # 顯示機率最高的幾個號碼
                print(f"\n與24配對時，下期產生尾數{target_digit}機率最高的號碼:")
                top_pairs = [(num, stats) for num, stats in sorted_paired if stats['with_target_digit'] > 0]
                for num, stats in top_pairs[:5]:  # 顯示前5名
                    percentage = (stats['with_target_digit'] / stats['total']) * 100
                    print(f"  數字 {num:2d}: 機率 {percentage:.2f}% (共 {stats['total']} 次，成功 {stats['with_target_digit']} 次)")
            else:
                print("沒有足夠的數據進行分析")
        
        # 整體前三名尾數分析
        print(f"\n=== 整體前三名尾數分析 ===")
        
        # 找出與24配對時能提高前三名尾數整體機率的號碼
        paired_numbers_stats = {}
        
        for idx in indices_with_24:
            if idx + 1 < len(draws):  # 確保有下一期
                current_draw = draws[idx]  # 當期號碼（包含24）
                next_draw = draws[idx + 1]  # 下一期號碼
                
                # 檢查當期除了24外的其他號碼
                other_numbers = [num for num in current_draw if num != 24]
                
                # 檢查下一期是否包含前三名尾數
                next_draw_digits = [n % 10 for n in next_draw]
                has_top3_digit = any(digit in target_digits for digit in next_draw_digits)
                
                for num in other_numbers:
                    if num not in paired_numbers_stats:
                        paired_numbers_stats[num] = {'total': 0, 'with_top3_digits': 0}
                    
                    paired_numbers_stats[num]['total'] += 1
                    
                    if has_top3_digit:
                        paired_numbers_stats[num]['with_top3_digits'] += 1
        
        # 顯示結果
        print(f"與24同在一期的號碼對應下一期出現前三名尾數的統計:")
        print("(格式: 號碼: [出現次數, 下期有前三名尾數次數, 百分比])")
        
        # 過濾掉總出現次數少於2次的號碼
        significant_pairs = {num: stats for num, stats in paired_numbers_stats.items() if stats['total'] >= 2}
        
        if significant_pairs:
            sorted_paired = sorted(significant_pairs.items(), 
                                 key=lambda x: x[1]['with_top3_digits']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                 reverse=True)
            
            # 顯示前10名
            for num, stats in sorted_paired[:10]:
                if stats['total'] > 0:
                    percentage = (stats['with_top3_digits'] / stats['total']) * 100
                    print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_top3_digits']}, {percentage:.2f}%]")
            
            # 顯示機率最高的幾個號碼
            print(f"\n與24配對時，下期產生前三名尾數機率最高的號碼:")
            top_pairs = [(num, stats) for num, stats in sorted_paired if stats['with_top3_digits'] > 0]
            for num, stats in top_pairs[:10]:  # 顯示前10名
                percentage = (stats['with_top3_digits'] / stats['total']) * 100
                print(f"  數字 {num:2d}: 機率 {percentage:.2f}% (共 {stats['total']} 次，成功 {stats['with_top3_digits']} 次)")

if __name__ == "__main__":
    analyze_number_24_following()