# 樂透號碼分析系統

## 專案簡介

這是一個樂透號碼統計分析工具，可以分析歷史開獎數據，並預測下一期最可能出現的號碼。

## 功能特色

- ✅ 讀取並解析樂透開獎歷史數據
- ✅ 多時間段分析（全期、近100期、近50期、近20期）
- ✅ 統計每個號碼的出現頻率
- ✅ 找出前三名最常出現的號碼
- ✅ 加權綜合分析，預測下一期最可能出現的號碼
- ✅ 視覺化圖表展示分析結果

## 系統需求

- Python 3.8 或以上版本
- 必要套件：
  - pandas
  - numpy
  - matplotlib
  - seaborn

## 安裝步驟

1. 確保已安裝 Python 3.8+

2. 安裝必要套件：
```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

1. 確保 `data_compare_lines1.csv` 檔案在同一目錄下

2. 執行分析程式：
```bash
python lottery_analysis.py
```

3. 程式會自動：
   - 載入歷史開獎數據
   - 進行多維度統計分析
   - 顯示前三名最常出現的號碼
   - 預測下一期最可能出現的號碼
   - 生成視覺化圖表（儲存為 `lottery_frequency_analysis.png`）

### 數據格式

CSV 檔案格式範例：
```
1→13,21,23,27,31,49
2→12,19,23,42,44,48
3→26,28,35,39,44,45
...
```

每行格式：`期數→號碼1,號碼2,號碼3,號碼4,號碼5,號碼6`

## 分析方法

### 1. 頻率統計
程式會統計每個號碼在不同時間段的出現次數：
- **全期分析**：分析所有歷史數據
- **近100期分析**：分析最近100期數據
- **近50期分析**：分析最近50期數據
- **近20期分析**：分析最近20期數據

### 2. 加權預測
使用加權演算法綜合各時間段的數據：
- 全期數據：權重 × 1
- 近100期：權重 × 2
- 近50期：權重 × 3
- 近20期：權重 × 5

較近期的數據給予較高權重，以反映最新趨勢。

### 3. 視覺化分析
生成四個圖表：
1. 全期號碼出現頻率（紅色標記前三名）
2. 最近100期號碼出現頻率（紅色標記前三名）
3. 最近50期號碼出現頻率（紅色標記前三名）
4. 前10名熱門號碼比較圖

## 輸出結果

### 終端輸出
```
==============================================================
樂透號碼統計分析報告
==============================================================

【全期分析】
前三名最常出現的號碼:
  第1名: 號碼 XX - 出現 XXX 次 (X.XX%)
  第2名: 號碼 XX - 出現 XXX 次 (X.XX%)
  第3名: 號碼 XX - 出現 XXX 次 (X.XX%)

【最近100期分析】
...

【下一期預測 - 綜合分析】
根據加權分析，下一期最可能出現的前三名號碼:
  第1名: 號碼 XX (加權分數: XXX)
         - 全期出現: XX 次
         - 近100期: XX 次
         - 近50期: XX 次
         - 近20期: XX 次
```

### 圖表輸出
- 檔案名稱：`lottery_frequency_analysis.png`
- 格式：PNG
- 解析度：300 DPI
- 包含四個子圖的完整分析圖表

## 程式架構

```
LotteryAnalyzer 類別
├── __init__()           # 初始化分析器
├── load_data()          # 載入CSV數據
├── analyze_frequency()  # 分析號碼頻率
├── get_top_numbers()    # 獲取前N名號碼
├── analyze_patterns()   # 執行完整分析
└── visualize_frequency() # 生成視覺化圖表
```

## 注意事項

⚠️ **重要提醒**：
- 本程式僅供統計分析參考
- 樂透開獎具有隨機性，過去數據不能保證未來結果
- 請理性購買，量力而為

## 自訂設定

### 修改分析時間段
在 `analyze_patterns()` 方法中修改：
```python
recent_100_freq = self.analyze_frequency(100)  # 改為其他數字
```

### 修改預測數量
在 `get_top_numbers()` 方法調用時修改 `top_n` 參數：
```python
top_all = self.get_top_numbers(all_freq, 5)  # 改為顯示前5名
```

### 修改加權比例
在 `analyze_patterns()` 方法中修改權重：
```python
weighted_scores[number] = weighted_scores.get(number, 0) + count * 5  # 修改權重值
```

## 授權

本專案僅供學習和研究使用。

## 聯絡資訊

如有問題或建議，歡迎提出 Issue。

---

**最後更新：2024年**
