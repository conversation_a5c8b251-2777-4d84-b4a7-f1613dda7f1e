import csv
from collections import Counter

def analyze_number_24_44_third_number():
    """
    分析當數字24和44同時出現時，加入第三個號碼對提升尾數2、8、7出現機率的影響
    """
    # 讀取CSV文件
    with open('data_compare_lines1.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字24和44同時出現的行的索引
    indices_with_24_44 = []
    for i, draw in enumerate(draws):
        if 24 in draw and 44 in draw:
            indices_with_24_44.append(i)
    
    print(f"數字24和44同時在以下期數出現: {[idx+1 for idx in indices_with_24_44]}")
    print(f"總共出現 {len(indices_with_24_44)} 次")
    
    if not indices_with_24_44:
        print("沒有找到24和44同時出現的期數")
        return
    
    # 分析目標尾數
    target_digits = [2, 8, 7]
    
    for target_digit in target_digits:
        print(f"\n=== 分析當24和44同時出現時，加入第三個號碼對提升尾數{target_digit}出現機率的影響 ===")
        
        # 統計與24,44同時出現的第三個號碼對目標尾數的影響
        third_number_stats = {}
        
        for idx in indices_with_24_44:
            if idx + 1 < len(draws):  # 確保有下一期
                current_draw = draws[idx]  # 當期號碼（包含24和44）
                next_draw = draws[idx + 1]  # 下一期號碼
                
                # 找出當期除了24和44之外的其他號碼（即第三個號碼）
                other_numbers = [num for num in current_draw if num != 24 and num != 44]
                
                # 檢查下一期是否包含目標尾數
                next_draw_digits = [n % 10 for n in next_draw]
                has_target_digit = target_digit in next_draw_digits
                
                for num in other_numbers:
                    if num not in third_number_stats:
                        third_number_stats[num] = {'total': 0, 'with_target_digit': 0}
                    
                    third_number_stats[num]['total'] += 1
                    if has_target_digit:
                        third_number_stats[num]['with_target_digit'] += 1
        
        # 過濾掉出現次數少於2次的號碼
        significant_third_numbers = {num: stats for num, stats in third_number_stats.items() if stats['total'] >= 2}
        
        if significant_third_numbers:
            print(f"與24,44同時出現至少2次且對下期尾數{target_digit}影響的統計:")
            print("(格式: 號碼: [共同出現次數, 下期有目標尾數次數, 成功率])")
            
            sorted_third_numbers = sorted(significant_third_numbers.items(), 
                                        key=lambda x: x[1]['with_target_digit']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                        reverse=True)
            
            for num, stats in sorted_third_numbers:
                success_rate = (stats['with_target_digit'] / stats['total']) * 100
                print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_target_digit']}, {success_rate:.2f}%]")
            
            # 顯示成功率最高的前10個號碼
            print(f"\n與24,44配對時，下期產生尾數{target_digit}機率最高的前10個號碼:")
            top_third_numbers = [(num, stats) for num, stats in sorted_third_numbers if stats['with_target_digit'] > 0]
            for num, stats in top_third_numbers[:10]:  # 顯示前10名
                success_rate = (stats['with_target_digit'] / stats['total']) * 100
                print(f"  數字 {num:2d}: 機率 {success_rate:.2f}% (共 {stats['total']} 次，成功 {stats['with_target_digit']} 次)")
        else:
            print(f"沒有足夠的數據分析第三個號碼對尾數{target_digit}的影響")

    # 綜合分析：同時提升三個目標尾數的號碼
    print(f"\n=== 綜合分析：同時提升尾數2、8、7出現機率的號碼 ===")
    
    combined_stats = {}
    
    for idx in indices_with_24_44:
        if idx + 1 < len(draws):  # 確保有下一期
            current_draw = draws[idx]  # 當期號碼（包含24和44）
            next_draw = draws[idx + 1]  # 下一期號碼
            
            # 找出當期除了24和44之外的其他號碼
            other_numbers = [num for num in current_draw if num != 24 and num != 44]
            
            # 檢查下一期是否包含任何一個目標尾數
            next_draw_digits = [n % 10 for n in next_draw]
            has_any_target_digit = any(digit in target_digits for digit in next_draw_digits)
            
            for num in other_numbers:
                if num not in combined_stats:
                    combined_stats[num] = {'total': 0, 'with_any_target_digit': 0}
                
                combined_stats[num]['total'] += 1
                if has_any_target_digit:
                    combined_stats[num]['with_any_target_digit'] += 1
    
    # 過濾掉出現次數少於2次的號碼
    significant_combined = {num: stats for num, stats in combined_stats.items() if stats['total'] >= 2}
    
    if significant_combined:
        print(f"與24,44同時出現至少2次且對下期產生尾數2/8/7任一的影響統計:")
        print("(格式: 號碼: [共同出現次數, 下期有目標尾數次數, 成功率])")
        
        sorted_combined = sorted(significant_combined.items(), 
                               key=lambda x: x[1]['with_any_target_digit']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                               reverse=True)
        
        for num, stats in sorted_combined:
            success_rate = (stats['with_any_target_digit'] / stats['total']) * 100
            print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_any_target_digit']}, {success_rate:.2f}%]")
        
        # 顯示成功率最高的前10個號碼
        print(f"\n與24,44配對時，下期產生尾數2/8/7任一機率最高的前10個號碼:")
        top_combined = [(num, stats) for num, stats in sorted_combined if stats['with_any_target_digit'] > 0]
        for num, stats in top_combined[:10]:  # 顯示前10名
            success_rate = (stats['with_any_target_digit'] / stats['total']) * 100
            print(f"  數字 {num:2d}: 機率 {success_rate:.2f}% (共 {stats['total']} 次，成功 {stats['with_any_target_digit']} 次)")

if __name__ == "__main__":
    analyze_number_24_44_third_number()
