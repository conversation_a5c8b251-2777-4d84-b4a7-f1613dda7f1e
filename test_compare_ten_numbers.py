import csv
from itertools import combinations

def read_csv_data(filename):
    """讀取CSV檔案並返回所有六個數字的組合"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        for line in file:
            numbers = [int(num.strip()) for num in line.strip().split(',')]
            data.append(numbers)
    return data

def generate_combinations(numbers, size=6):
    """從十個數字中生成六個數字的所有組合"""
    return list(combinations(numbers, size))

def compare_combinations(user_combinations, csv_data):
    """比對用戶組合與CSV數據，返回匹配結果"""
    results = {
        6: [],  # 6個重複
        5: [],  # 5個重複
        4: [],  # 4個重複
        3: [],  # 3個重複
        2: [],  # 2個重複
        1: [],  # 1個重複
        0: []   # 0個重複
    }
    
    for user_combo in user_combinations:
        user_set = set(user_combo)
        
        # 尋找最高匹配數
        max_matches = 0
        for csv_combo in csv_data:
            csv_set = set(csv_combo)
            matches = len(user_set.intersection(csv_set))
            max_matches = max(max_matches, matches)
        
        # 將組合添加到相應的匹配數組中
        results[max_matches].append(user_combo)
    
    return results

def display_results(results):
    """顯示比對結果"""
    print("="*50)
    print("比對結果：")
    print("="*50)
    
    for match_count in [6, 5, 4, 3, 2, 1, 0]:
        if results[match_count]:
            print(f"\n{match_count} 個重複號碼的組合數量: {len(results[match_count])}")
            if match_count >= 3:  # 顯示3個及以上重複的組合（可根據需要調整）
                print(f"具體組合：")
                for i, combo in enumerate(results[match_count][:20], 1):  # 增加顯示數量到前20個
                    print(f"  {i:2d}. {combo}")
                if len(results[match_count]) > 20:
                    print(f"  ... 還有 {len(results[match_count]) - 20} 個組合")
        else:
            print(f"\n{match_count} 個重複號碼的組合數量: 0")

def main():
    print("十個數字比對程式（測試版）")
    print("此程式將從預設的十個數字中生成所有可能的六個數字組合，")
    print("並與 data_compare_lines1.csv 中的數據進行比對。")
    print()
    
    # 預設的十個數字（可根據需要更改）
    user_numbers = [1, 5, 12, 18, 23, 27, 31, 35, 42, 48]
    print(f"使用的十個數字為: {user_numbers}")
    
    # 讀取CSV數據
    print("\n正在讀取 data_compare_lines1.csv ...")
    try:
        csv_data = read_csv_data('data_compare_lines1.csv')
        print(f"成功讀取 {len(csv_data)} 條數據")
    except FileNotFoundError:
        print("錯誤：找不到 data_compare_lines1.csv 檔案")
        return
    except Exception as e:
        print(f"讀取檔案時發生錯誤：{e}")
        return
    
    # 生成六個數字的組合
    print("\n正在生成六個數字的所有組合...")
    user_combinations = generate_combinations(user_numbers, 6)
    print(f"共生成 {len(user_combinations)} 組六個數字的組合")
    
    # 比對組合
    print("\n正在進行比對...")
    results = compare_combinations(user_combinations, csv_data)
    
    # 顯示結果
    display_results(results)
    
    print(f"\n總結：")
    print(f"- 6個重複號碼: {len(results[6])} 組")
    print(f"- 5個重複號碼: {len(results[5])} 組")
    print(f"- 4個重複號碼: {len(results[4])} 組")
    print(f"- 3個重複號碼: {len(results[3])} 組")
    print(f"- 2個重複號碼: {len(results[2])} 組")
    print(f"- 1個重複號碼: {len(results[1])} 組")
    print(f"- 0個重複號碼: {len(results[0])} 組")

if __name__ == "__main__":
    main()