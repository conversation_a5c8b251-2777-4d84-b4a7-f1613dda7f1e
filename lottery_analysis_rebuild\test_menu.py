#!/usr/bin/env python3
"""測試啟動選單功能"""

import sys
from pathlib import Path

# 添加項目路徑到系統路徑
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

try:
    from config import Config
    from data_loader import load_csv_data, validate_input_numbers
    from analyzer import LotteryAnalyzer, MatchResult
    from advanced_analyzer import AdvancedLotteryAnalyzer
    
    print("O 所有模組導入成功!")
    print(f"O Config.DEFAULT_CSV_FILE = {Config.DEFAULT_CSV_FILE}")
    
    # 測試載入數據
    try:
        analyzer = LotteryAnalyzer(Config.DEFAULT_CSV_FILE)
        print(f"O 成功載入 {len(analyzer.data)} 行數據")
    except FileNotFoundError:
        print(f"! 注意: 找不到預設檔案 {Config.DEFAULT_CSV_FILE}")
        # 嘗試使用相對路徑
        alt_path = '../data_compare_lines1.csv'
        analyzer = LotteryAnalyzer(alt_path)
        print(f"O 成功載入 {len(analyzer.data)} 行數據 (使用替代路徑)")
    
    # 測試基本比較功能
    test_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
    results = analyzer.compare_numbers(test_numbers)
    print(f"O 號碼比較功能正常運作")
    
    # 測試進階分析器
    advanced_analyzer = AdvancedLotteryAnalyzer(Config.DEFAULT_CSV_FILE)
    print(f"O 進階分析器創建成功")
    
    print("\nO 所有功能測試通過！啟動選單應該可以正常運行。")
    
except Exception as e:
    print(f"X 測試失敗: {e}")
    import traceback
    traceback.print_exc()