import csv
from collections import Counter
import os

def complete_analysis_3_4_with_third_number():
    """
    Complete analysis of how adding a third number to 3 and 4 affects the probability of last digits
    """
    # Check if file exists
    if not os.path.exists('data_compare_lines2.csv'):
        print("Error: data_compare_lines2.csv file not found")
        return
    
    # Read CSV file
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("Error: data_compare_lines2.csv file not found")
        return
    except UnicodeDecodeError:
        # If UTF-8 decoding fails, try other encoding
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("Error: Unable to decode the encoding of data_compare_lines2.csv file")
            return
    
    # Parse data
    draws = []
    for line in lines:
        # Remove line numbers and newlines, extract numbers
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # Find all draws that contain both 3 and 4
    indices_with_3_4 = []
    for i, draw in enumerate(draws):
        if 3 in draw and 4 in draw:
            indices_with_3_4.append(i)
    
    print(f"Total occurrences of 3 and 4 together: {len(indices_with_3_4)}")
    
    # Get the next draw numbers after 3 and 4 appear together
    next_draw_numbers_3_4_only = []
    for idx in indices_with_3_4:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers_3_4_only.extend(next_draw)
    
    # Analyze last digits when only 3 and 4 appear together
    last_digits_3_4_only = [num % 10 for num in next_draw_numbers_3_4_only]
    last_digit_counts_3_4_only = Counter(last_digits_3_4_only)
    
    print(f"\nWhen 3 and 4 appear together (29 occurrences), next draw last digit distribution:")
    for digit in sorted(last_digit_counts_3_4_only.keys()):
        count = last_digit_counts_3_4_only[digit]
        percentage = (count / len(last_digits_3_4_only)) * 10
        print(f"  Last digit {digit}: {count} times ({percentage:.2f}%)")
    
    most_common_3_4_only = last_digit_counts_3_4_only.most_common(1)[0]
    print(f"\nMost common last digit with 3 and 4 only: Last digit {most_common_3_4_only[0]} ({most_common_3_4_only[1]} times, {(most_common_3_4_only[1]/len(last_digits_3_4_only))*100:.2f}%)")
    
    # Now let's analyze adding different third numbers to 3 and 4
    print(f"\nAnalyzing adding different third numbers to 3 and 4 (only showing combinations with 2+ occurrences):")
    print("="*80)
    
    # Store results for analysis
    results = []
    
    # Try adding each number from 1 to 49 to see how it affects the most common digit
    for third_number in range(1, 50):
        if third_number == 3 or third_number == 4:
            continue  # Skip 3 and 4 as they're already in the combination
        
        # Find draws that contain 3, 4, and the third number
        indices_with_3_4_third = []
        for i, draw in enumerate(draws):
            if 3 in draw and 4 in draw and third_number in draw:
                indices_with_3_4_third.append(i)
        
        if len(indices_with_3_4_third) >= 2:  # Only analyze if there are at least 2 occurrences
            # Get next draw numbers after 3, 4, and third number appear together
            next_draw_numbers_3_4_third = []
            for idx in indices_with_3_4_third:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers_3_4_third.extend(next_draw)
            
            if next_draw_numbers_3_4_third:
                last_digits_3_4_third = [num % 10 for num in next_draw_numbers_3_4_third]
                last_digit_counts_3_4_third = Counter(last_digits_3_4_third)
                
                # Check if the most common digit is the same as with just 3 and 4
                most_common_3_4_third = last_digit_counts_3_4_third.most_common(1)[0]
                
                # Calculate the probability of the most common digit from 3,4-only analysis
                target_digit_count = last_digit_counts_3_4_third[most_common_3_4_only[0]] if most_common_3_4_only[0] in last_digit_counts_3_4_third else 0
                target_digit_percentage = (target_digit_count / len(last_digits_3_4_third)) * 100 if len(last_digits_3_4_third) > 0 else 0
                
                # Calculate the probability of the new most common digit
                most_common_percentage = (most_common_3_4_third[1] / len(last_digits_3_4_third)) * 100 if len(last_digits_3_4_third) > 0 else 0
                
                results.append({
                    'third_number': third_number,
                    'occurrences': len(indices_with_3_4_third),
                    'most_common_digit': most_common_3_4_third[0],
                    'most_common_count': most_common_3_4_third[1],
                    'most_common_percentage': most_common_percentage,
                    'original_target_count': target_digit_count,
                    'original_target_percentage': target_digit_percentage
                })
    
    # Sort results by occurrences in descending order
    results.sort(key=lambda x: x['occurrences'], reverse=True)
    
    # Print results
    for result in results:
        print(f"\nAdding number {result['third_number']} to 3 and 4:")
        print(f"  Occurrences: {result['occurrences']}")
        print(f"  Most common last digit: {result['most_common_digit']} ({result['most_common_count']} times, {result['most_common_percentage']:.2f}%)")
        print(f"  Probability of original most common digit ({most_common_3_4_only[0]}): {result['original_target_count']} times, {result['original_target_percentage']:.2f}%")
        
        # Check if the probability increased compared to 3 and 4 only
        original_percentage = (most_common_3_4_only[1] / len(last_digits_3_4_only)) * 10
        if result['original_target_percentage'] > original_percentage:
            print(f"  *** IMPROVEMENT: Probability of digit {most_common_3_4_only[0]} increased from {original_percentage:.2f}% to {result['original_target_percentage']:.2f}% ***")
    
    # Summary: Find combinations that improve the probability of the original most common digit (5)
    print(f"\nSUMMARY: Combinations that improve the probability of digit {most_common_3_4_only[0]} (original: {original_percentage:.2f}%):")
    print("-" * 60)
    improved_combinations = [r for r in results if r['original_target_percentage'] > original_percentage]
    
    if improved_combinations:
        for result in improved_combinations:
            print(f"  Numbers 3, 4, {result['third_number']}: {result['original_target_percentage']:.2f}% (from {original_percentage:.2f}%) - {result['occurrences']} occurrences")
    else:
        print("  No combinations found that improve the probability of the original most common digit.")
    
    # Find combinations with high probability of the original most common digit
    print(f"\nCombinations with high probability of digit {most_common_3_4_only[0]} (>= 10%):")
    print("-" * 60)
    high_prob_combinations = [r for r in results if r['original_target_percentage'] >= 10]
    
    if high_prob_combinations:
        for result in high_prob_combinations:
            print(f" Numbers 3, 4, {result['third_number']}: {result['original_target_percentage']:.2f}% - {result['occurrences']} occurrences")
    else:
        print("  No combinations found with >= 10% probability of the original most common digit.")

if __name__ == "__main__":
    complete_analysis_3_4_with_third_number()