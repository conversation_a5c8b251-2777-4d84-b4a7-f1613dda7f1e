"""資料載入模組"""

import csv
from pathlib import Path
from typing import List, Set
import pandas as pd


def load_csv_data(filename: str, expected_count: int = 6) -> List[Set[int]]:
    """
    載入CSV資料並返回數字集合列表
    
    Args:
        filename: CSV檔案路徑
        expected_count: 預期每行數字數量
    
    Returns:
        包含數字集合的列表
    """
    data = []
    
    # 嘗試使用pandas載入（更高效）
    try:
        df = pd.read_csv(filename, header=None)
        for _, row in df.iterrows():
            numbers = {int(num) for num in row.dropna() if str(num).strip()}
            if numbers:  # 只添加非空集合
                data.append(numbers)
    except Exception:
        # 如果pandas載入失敗，使用傳統方法
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            for row in reader:
                numbers = {int(num.strip()) for num in row if num.strip()}
                if numbers:  # 只添加非空集合
                    data.append(numbers)
    
    return data


def validate_input_numbers(numbers: List[int], expected_count: int = 8, 
                          min_val: int = 1, max_val: int = 49) -> bool:
    """
    驗證輸入號碼的有效性
    
    Args:
        numbers: 要驗證的號碼列表
        expected_count: 預期號碼數量
        min_val: 最小值
        max_val: 最大值
    
    Returns:
        驗證是否通過
    """
    if len(numbers) != expected_count:
        raise ValueError(f"必須輸入{expected_count}個號碼")
    
    if len(set(numbers)) != len(numbers):
        raise ValueError("號碼不能重複")
    
    if not all(min_val <= num <= max_val for num in numbers):
        raise ValueError(f"號碼必須在{min_val}到{max_val}之間")
    
    return True