import json
from collections import Counter

def load_results():
    """Load results.json file"""
    with open('results.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_lottery_predictions():
    """Detailed lottery number prediction analysis"""
    # Provided number combinations
    provided_combinations = [
        [11,16,20,31,39,41],
        [3,15,16,31,32,45],
        [14,20,25,42,44,49],
        [13,22,23,33,39,40],
        [5,8,11,12,18,39],
        [3,4,7,10,18,21]
    ]
    
    # Load results.json
    results = load_results()
    
    print("Detailed Lottery Number Prediction Analysis Report")
    print("="*60)
    
    # Analyze match rate of each combination with high-frequency numbers
    print("\n1. Match Analysis of Each Combination with High-Frequency Numbers:")
    position_frequency_score = {}  # Match score for each position
    
    for i, combination in enumerate(provided_combinations):
        print(f"\nCombination {i+1} {combination}:")
        combination_score = 0
        
        for num in combination:
            # Check which positions this number is a high-frequency number in
            positions_for_this_num = []
            for pos in range(1, 7):  # Analyze first 6 positions
                pos_key = str(pos)
                if pos_key in results:
                    top_10_numbers = [item['number'] for item in results[pos_key][:10]]
                    if num in top_10_numbers:
                        rank = top_10_numbers.index(num) + 1
                        positions_for_this_num.append((pos, rank))
                        # Score based on rank (higher rank = higher score)
                        combination_score += (11 - rank)  # Rank 1 = 10 points, Rank 10 = 1 point
                        
            if positions_for_this_num:
                print(f"  Number {num} is a high-frequency number at position {positions_for_this_num}")
        
        print(f" Combination {i+1} total match score: {combination_score}")
        
        # Update score for each position
        for num in combination:
            for pos in range(1, 7):
                pos_key = str(pos)
                if pos_key in results:
                    top_10_numbers = [item['number'] for item in results[pos_key][:10]]
                    if num in top_10_numbers:
                        rank = top_10_numbers.index(num) + 1
                        if pos not in position_frequency_score:
                            position_frequency_score[pos] = {}
                        if num not in position_frequency_score[pos]:
                            position_frequency_score[pos][num] = 0
                        position_frequency_score[pos][num] += (11 - rank)
    
    print(f"\n2. High-Frequency Number Match Scores by Position:")
    for pos in range(1, 7):
        print(f"\nPosition {pos} High-Frequency Match Scores:")
        if pos in position_frequency_score:
            sorted_nums = sorted(position_frequency_score[pos].items(), key=lambda x: x[1], reverse=True)
            for num, score in sorted_nums[:5]:  # Show top 5 high-scoring numbers
                print(f"  Number {num}: {score} points")
        else:
            print("  No matching numbers")
    
    # Analyze possibility of number 49 appearing
    print(f"\n3. Analysis of Number 49 Appearance Possibility:")
    positions_with_49 = []
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            top_10_numbers = [item['number'] for item in results[pos_key][:10]]
            if 49 in top_10_numbers:
                rank = top_10_numbers.index(49) + 1
                positions_with_49.append((pos, rank))
    
    if positions_with_49:
        print("  Number 49 is a high-frequency number at the following positions:")
        for pos, rank in positions_with_49:
            print(f"    Position {pos}: Rank {rank}")
    else:
        print("  Number 49 is not a high-frequency number in the first 6 positions")
    
    # Check if number 49 appears in provided combinations
    appearances_of_49 = []
    for i, combination in enumerate(provided_combinations):
        if 49 in combination:
            appearances_of_49.append(i+1)
    
    if appearances_of_49:
        print(f" Number 49 appears in combination(s) {', '.join(map(str, appearances_of_49))}")
    else:
        print("  Number 49 does not appear in any provided combinations")
    
    # Predict next period numbers based on analysis
    print(f"\n4. Next Period Number Predictions:")
    
    predicted_numbers = []
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            top_numbers = results[pos_key][:10] # Take top 10 high-frequency numbers
            top_nums_list = [item['number'] for item in top_numbers]
            
            # Check how many of these high-frequency numbers appear in provided combinations
            matching_numbers = []
            for num in top_nums_list:
                count = 0
                for combination in provided_combinations:
                    if num in combination:
                        count += 1
                if count >= 3:  # If appears in 3 or more combinations, consider high probability
                    matching_numbers.append((num, count))
            
            # If no numbers appear in 3+ combinations, select the one with most appearances
            if matching_numbers:
                best_match = max(matching_numbers, key=lambda x: x[1])
                predicted_numbers.append(best_match[0])
                print(f" Position {pos} prediction: {best_match[0]} (appears in {best_match[1]} combinations)")
            else:
                # Select the highest ranked number in that position
                best_num = top_numbers[0]['number']
                predicted_numbers.append(best_num)
                print(f"  Position {pos} prediction: {best_num} (highest frequency in position)")
    
    print(f"\n5. Final Prediction Result: {predicted_numbers}")
    
    # Validate prediction result reasonableness
    print(f"\n6. Prediction Result Verification:")
    print(f"Predicted Numbers: {predicted_numbers}")
    
    # Check performance of predicted numbers in overall data
    for i, pred_num in enumerate(predicted_numbers):
        pos = i + 1
        pos_key = str(pos)
        if pos_key in results:
            all_nums_in_pos = [item['number'] for item in results[pos_key]]
            if pred_num in all_nums_in_pos:
                rank_in_pos = all_nums_in_pos.index(pred_num) + 1
                percentage = next(item['percentage'] for item in results[pos_key] if item['number'] == pred_num)
                print(f"  Position {pos} number {pred_num}: Rank {rank_in_pos}, Frequency {percentage}%")
    
    # Summary
    print(f"\n7. Summary:")
    print(f"  - Analysis based on 6 provided combinations and frequency data in results.json")
    print(f"  - Predicted next period numbers: {predicted_numbers}")
    print(f"  - This prediction is based on 'high-frequency numbers appearing in specific positions' and 'appearing in multiple provided combinations'")
    print(f" - For number 49: {'Likely to appear' if len(positions_with_49) > 0 else 'Low probability of appearance'}")
    
    return predicted_numbers

if __name__ == "__main__":
    predictions = analyze_lottery_predictions()