# 啟動選單使用說明

## 執行啟動選單

```bash
python menu_launcher.py
```

## 功能選單

### 1. 號碼比較
- 比較您指定的8個號碼與CSV檔案中的資料
- 顯示完全匹配和部分匹配的結果
- 提供各號碼的出現統計

### 2. 進階分析
- 對整個CSV檔案進行全面分析
- 顯示頻率最高的號碼
- 分析位置分布、範圍分布、和值統計等
- 提供奇偶數分布資訊

### 3. 視覺化圖表
- 生成數據的視覺化圖表
- 包含頻率分布、範圍分布、和值分布、奇偶分布圖表
- 可自訂輸出檔案名稱

### 4. 號碼統計
- 查看特定8個號碼在CSV中的出現次數
- 顯示總體統計資訊
- 標示出現最多和最少的號碼

### 5. 批次比較
- 一次比較多組號碼（每組8個）
- 輸入格式：`1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16`
- 顯示每組的比較結果摘要

### 6. 互動模式
- 持續互動模式，可連續比較多組號碼
- 輸入 'q' 或 'quit' 離開模式
- 即時顯示比較結果

## 檔案選擇

所有功能都支援選擇不同的CSV檔案進行分析，預設使用 `../data_compare_lines1.csv`。

## 錯誤處理

- 檔案不存在時會顯示錯誤訊息
- 號碼格式錯誤時會提示重新輸入
- 號碼範圍或數量不符時會要求重新輸入

## 系統要求

- Python 3.7 或更高版本
- 已安裝所需依賴套件（執行 `pip install -r requirements.txt`）

## 範例

執行啟動選單：

```bash
cd lottery_analysis_rebuild
python menu_launcher.py
```

選擇選項後按照提示操作即可。