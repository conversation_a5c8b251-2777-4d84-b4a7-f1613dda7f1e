import csv
from io import <PERSON><PERSON>
from itertools import combinations
from collections import Counter, defaultdict

def parse_csv_data(csv_content):
    """
    Parses CSV content into a list of lists of integers.
    Each inner list represents a line of numbers.
    """
    data = []
    # Use StringIO to treat the string content as a file
    csvfile = StringIO(csv_content)
    reader = csv.reader(csvfile)
    for row in reader:
        # Convert each number string to an integer
        data.append([int(num) for num in row])
    return data

def generate_combinations(numbers, lengths):
    """
    Generates combinations of specified lengths from a list of numbers.
    """
    all_combinations = []
    for length in lengths:
        for combo in combinations(numbers, length):
            all_combinations.append(tuple(sorted(combo)))
    return all_combinations

def analyze_last_draw_combinations_in_history(all_draws):
    """
    Analyzes the occurrences of combinations from the last draw in all previous draws.
    """
    if not all_draws:
        return Counter()

    last_draw_numbers = all_draws[-1]
    combinations_from_last_draw = generate_combinations(last_draw_numbers, [2, 3, 4, 5])

    historical_occurrence_counts = Counter()
    
    # Iterate through all draws except the last one
    for i in range(len(all_draws) - 1):
        current_historical_draw_numbers = set(all_draws[i])
        for combo in combinations_from_last_draw:
            # Check if all numbers in the combination from the last draw are present in the current historical draw
            if all(num in current_historical_draw_numbers for num in combo):
                historical_occurrence_counts[combo] += 1
    return historical_occurrence_counts

def analyze_next_numbers_for_combo(all_draws, target_combo):
    """
    Finds the most common numbers that appear in the draw following the one where target_combo appeared.
    """
    next_numbers = []
    target_combo_set = set(target_combo)
    for i in range(len(all_draws) - 1):
        if target_combo_set.issubset(set(all_draws[i])):
            next_numbers.extend(all_draws[i+1])
    return Counter(next_numbers).most_common(3)

def analyze_conditional_backtracking(all_draws, target_numbers, lookback_period=5):
    """
    條件式反向追蹤分析
    :param all_draws: 所有開獎結果
    :param target_numbers: 要追蹤的目標號碼列表
    :param lookback_period: 要回顧的期數
    :return: 包含分析結果的字典
    """
    results = defaultdict(lambda: {
        'count': 0,
        'preceding_numbers': defaultdict(int),
        'preceding_pairs': defaultdict(int),
        'preceding_triplets': defaultdict(int),
        'gap_distribution': defaultdict(int)
    })
    
    for target in target_numbers:
        for i in range(lookback_period, len(all_draws)):
            current_draw = set(all_draws[i])
            if target in current_draw:
                results[target]['count'] += 1
                
                # 分析前幾期的號碼
                for j in range(1, lookback_period + 1):
                    prev_draw = all_draws[i - j]
                    
                    # 統計單個號碼出現頻率
                    for num in prev_draw:
                        results[target]['preceding_numbers'][num] += 1
                    
                    # 統計數字對出現頻率
                    for pair in combinations(prev_draw, 2):
                        results[target]['preceding_pairs'][tuple(sorted(pair))] += 1
                    
                    # 統計間隔期數
                    results[target]['gap_distribution'][j] += 1
                
                # 統計前一期出現的三個數組合
                if i > 0:
                    prev_draw = all_draws[i - 1]
                    for triplet in combinations(prev_draw, 3):
                        results[target]['preceding_triplets'][tuple(sorted(triplet))] += 1
    
    return results

def print_backtracking_results(results, lookback_period=5, top_n=5):
    """打印反向追蹤分析結果
    :param results: 分析結果字典
    :param lookback_period: 回顧期數，用於顯示
    :param top_n: 顯示前N個結果
    """
    for target, data in results.items():
        print(f"\n=== 號碼 {target} 的反向追蹤分析 (出現次數: {data['count']}) ===")
        
        # 打印前N個最常出現的前置號碼
        top_numbers = sorted(data['preceding_numbers'].items(), key=lambda x: -x[1])[:top_n]
        print(f"\n前{lookback_period}期內最常出現的號碼:")
        for num, count in top_numbers:
            print(f"  號碼 {num}: {count}次 ({count/data['count']*100:.1f}%)")
        
        # 打印前N個最常出現的數字對
        top_pairs = sorted(data['preceding_pairs'].items(), key=lambda x: -x[1])[:top_n]
        print(f"\n前{lookback_period}期內最常出現的數字對:")
        for pair, count in top_pairs:
            print(f"  組合 {pair}: {count}次 ({count/data['count']*100:.1f}%)")
        
        # 打印前N個最常出現的三個數組合
        top_triplets = sorted(data['preceding_triplets'].items(), key=lambda x: -x[1])[:top_n]
        print(f"\n前一期最常出現的三個數組合:")
        for triplet, count in top_triplets:
            print(f"  組合 {triplet}: {count}次 ({count/data['count']*100:.1f}%)")
        
        # 打印間隔期數分佈
        print("\n間隔期數分佈:")
        for gap in sorted(data['gap_distribution'].keys()):
            print(f"  間隔{gap}期: {data['gap_distribution'][gap]}次 ({data['gap_distribution'][gap]/data['count']*100:.1f}%)")

def main():
    try:
        with open('data_compare_lines1.csv', 'r') as f:
            csv_content = f.read()
    except FileNotFoundError:
        print("Error: data_compare_lines1.csv not found.")
        return

    all_draws = parse_csv_data(csv_content)
    
    # 1. 原始分析
    historical_occurrence_counts = analyze_last_draw_combinations_in_history(all_draws)
    four_num_combinations = Counter({combo: count for combo, count in historical_occurrence_counts.items() if len(combo) == 4})

    print("\n--- 各四個數字組合的下一期熱門號碼分析 ---")
    for combo, _ in sorted(four_num_combinations.items()):
        top_3_next_numbers = analyze_next_numbers_for_combo(all_draws, combo)
        print(f"\n組合 {combo} 出現後，下一期最常出現的前三個數字是:")
        for num, count in top_3_next_numbers:
            print(f"  數字: {num}, 出現次數: {count}")
    
    # 2. 新增：條件式反向追蹤分析
    print("\n\n=== 條件式反向追蹤分析 ===")
    # 分析最近一期開出的號碼
    if len(all_draws) > 0:
        last_draw = all_draws[-1]
        print(f"\n分析最近一期開出的號碼: {sorted(last_draw)}")
        
        # 對最近一期開出的每個號碼進行反向追蹤分析
        results = analyze_conditional_backtracking(all_draws, last_draw, lookback_period=5)
        print_backtracking_results(results, lookback_period=5)
    
    # 3. 新增：詳細的最後一筆數字反向追蹤分析
    print("\n\n=== 最後一筆數字詳細反向追蹤分析 ===")
    if len(all_draws) > 0:
        last_draw = all_draws[-1]
        print(f"最後一期開獎號碼: {sorted(last_draw)}")
        
        # 分析最後一期每個號碼在歷史上的出現模式
        for i, number in enumerate(sorted(last_draw)):
            print(f"\n--- 追蹤號碼 {number} 的歷史模式 ---")
            
            # 找出該號碼在歷史中出現的所有期數
            occurrences = []
            for idx, draw in enumerate(all_draws[:-1]):  # 排除最後一期
                if number in draw:
                    occurrences.append(idx)
            
            print(f"號碼 {number} 在歷史中出現 {len(occurrences)} 次")
            
            if occurrences:
                print("出現期數 (倒序顯示最近的):")
                # 顯示最近的10次出現
                recent_occurrences = occurrences[-10:][::-1]
                for idx in recent_occurrences:
                    gap = len(all_draws) - 1 - idx  # 計算與最後一期的間隔
                    print(f"  第 {idx+1} 期: {sorted(all_draws[idx])} (間隔 {gap} 期)")
                
                # 分析該號碼出現前一期的號碼模式
                preceding_numbers = Counter()
                for idx in occurrences:
                    if idx > 0:  # 確保有前一期
                        preceding_draw = all_draws[idx - 1]
                        for num in preceding_draw:
                            preceding_numbers[num] += 1
                
                if preceding_numbers:
                    print(f"\n號碼 {number} 出現前一期最常見的號碼 (前10名):")
                    for num, count in preceding_numbers.most_common(10):
                        percentage = count / len(occurrences) * 100
                        print(f"  號碼 {num}: {count} 次 ({percentage:.1f}%)")
                
                # 分析該號碼出現前兩期的號碼模式
                two_before_numbers = Counter()
                for idx in occurrences:
                    if idx > 1:  # 確保有前兩期
                        two_before_draw = all_draws[idx - 2]
                        for num in two_before_draw:
                            two_before_numbers[num] += 1
                
                if two_before_numbers:
                    print(f"\n號碼 {number} 出現前兩期最常見的號碼 (前10名):")
                    for num, count in two_before_numbers.most_common(10):
                        percentage = count / len(occurrences) * 100
                        print(f"  號碼 {num}: {count} 次 ({percentage:.1f}%)")
    
    # 也可以分析特定號碼
    specific_numbers = [1, 7, 15, 23, 35, 49]  # 可以替換為您感興趣的號碼
    print(f"\n\n=== 特定號碼反向追蹤分析: {specific_numbers} ===")
    specific_results = analyze_conditional_backtracking(all_draws, specific_numbers, lookback_period=5)
    print_backtracking_results(specific_results, lookback_period=5)
    
    # 4. 新增：1到49號碼的反向追蹤分析 - 前7期內最常出現的號碼
    print("\n\n=== 1到49號碼反向追蹤分析 - 前7期內最常出現的號碼 ===")
    if len(all_draws) > 7:
        # 獲取最後7期的號碼
        last_7_draws = all_draws[-7:]
        print(f"最後7期開獎號碼:")
        for i, draw in enumerate(last_7_draws):
            print(f" 第{len(all_draws)-6+i}期: {sorted(draw)}")
        
        # 計算1到49每個號碼在歷史中出現後，前7期內最常出現的號碼
        number_cooccurrence = defaultdict(lambda: Counter())
        for num in range(1, 50):
            for idx, draw in enumerate(all_draws[7:], 7):  # 從第8期開始，確保前7期存在
                if num in draw:
                    # 統計前7期內出現的號碼
                    for prev_idx in range(idx-7, idx):
                        for prev_num in all_draws[prev_idx]:
                            number_cooccurrence[num][prev_num] += 1
        
        # 顯示每個號碼在前7期內最常出現的伴隨號碼
        print("\n各號碼出現前7期內最常出現的號碼 (前5名):")
        for num in range(1, 50):
            if num in number_cooccurrence:
                top_5 = number_cooccurrence[num].most_common(5)
                print(f"\n號碼 {num} 出現前7期內最常出現的號碼:")
                for assoc_num, count in top_5:
                    percentage = count / sum(number_cooccurrence[num].values()) * 100
                    print(f"  號碼 {assoc_num}: {count} 次 ({percentage:.1f}%)")
            else:
                print(f"\n號碼 {num} 在歷史中未出現過")

if __name__ == "__main__":
    main()
