import csv
from collections import Counter
import os

def analyze_number_24_44_pair():
    """
    分析當數字24和44同時出現在同一期時，下一期各尾數的出現次數
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines1.csv'):
        print("錯誤: 找不到 data_compare_lines1.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines1.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines1.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果 UTF-8 解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines1.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解讀 data_compare_lines1.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字24和44同時出現的行的索引
    indices_with_24_44 = []
    for i, draw in enumerate(draws):
        if 24 in draw and 44 in draw:
            indices_with_24_44.append(i)
    
    print(f"數字24和44同時在以下期數出現: {[idx+1 for idx in indices_with_24_44]}")
    print(f"總共出現 {len(indices_with_24_44)} 次")
    
    # 收集這些期數下一期的所有數字，並分析尾數
    next_draw_numbers = []
    for idx in indices_with_24_44:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
            print(f"第 {idx+1} 期({draws[idx]}) -> 第 {idx+2} 期({next_draw})")
    
    if next_draw_numbers:
        print(f"\n當24和44同時出現後，下一期總共出現 {len(next_draw_numbers)} 個號碼")
        
        # 分析下一期號碼的尾數
        last_digits = [num % 10 for num in next_draw_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\n下一期號碼的尾數出現次數統計:")
        for digit in sorted(last_digit_counts.keys()):
            print(f" 尾數 {digit}: {last_digit_counts[digit]} 次")
        
        # 找出出現最多的尾數
        most_common_digit = last_digit_counts.most_common(1)[0]
        print(f"\n當24和44同時出現後，下一期出現最多的尾數是: 尾數 {most_common_digit[0]} (出現 {most_common_digit[1]} 次)")
        
        # 顯示前3名尾數
        print(f"\n前3名尾數:")
        top_3_digits = last_digit_counts.most_common(3)
        for i, (digit, count) in enumerate(top_3_digits, 1):
            percentage = (count / len(last_digits)) * 100
            print(f" {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")
        
        # 顯示該尾數包含哪些號碼
        numbers_with_most_common_digit = [num for num in next_draw_numbers if num % 10 == most_common_digit[0]]
        number_counts = Counter(numbers_with_most_common_digit)
        print(f"\n尾數 {most_common_digit[0]} 包含的號碼及其次數:")
        for num, count in sorted(number_counts.items()):
            print(f"  數字 {num}: {count} 次")
    
    # 比較與只出現24或只出現44時的差異
    print(f"\n=== 比較分析 ===")
    
    # 找到只有24出現（不含44）的期數
    indices_with_24_only = [i for i in range(len(draws)) if 24 in draws[i] and 44 not in draws[i]]
    
    # 找到只有44出現（不含24）的期數
    indices_with_44_only = [i for i in range(len(draws)) if 44 in draws[i] and 24 not in draws[i]]
    
    # 分析只有24時的下一期尾數
    if indices_with_24_only:
        next_24_only_numbers = []
        for idx in indices_with_24_only:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_24_only_numbers.extend(next_draw)
        
        if next_24_only_numbers:
            last_digits_24_only = [num % 10 for num in next_24_only_numbers]
            last_digit_counts_24_only = Counter(last_digits_24_only)
            
            print(f"\n只有24出現（不含44）時，下一期的尾數分布:")
            top_3_24_only = last_digit_counts_24_only.most_common(3)
            for i, (digit, count) in enumerate(top_3_24_only, 1):
                percentage = (count / len(last_digits_24_only)) * 100
                print(f" {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")
    
    # 分析只有44時的下一期尾數
    if indices_with_44_only:
        next_44_only_numbers = []
        for idx in indices_with_44_only:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_44_only_numbers.extend(next_draw)
        
        if next_44_only_numbers:
            last_digits_44_only = [num % 10 for num in next_44_only_numbers]
            last_digit_counts_44_only = Counter(last_digits_44_only)
            
            print(f"\n只有44出現（不含24）時，下一期的尾數分布:")
            top_3_44_only = last_digit_counts_44_only.most_common(3)
            for i, (digit, count) in enumerate(top_3_44_only, 1):
                percentage = (count / len(last_digits_44_only)) * 100
                print(f" {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")
    
    # 分析24和44同時出現時，當期其他號碼對下一期尾數的影響
    if indices_with_24_44:
        print(f"\n=== 當24和44同時出現時，當期其他號碼對下一期尾數{most_common_digit[0]}的影響 ===")
        
        paired_numbers_stats = {}
        
        for idx in indices_with_24_44:
            if idx + 1 < len(draws):
                current_draw = draws[idx]  # 當期號碼（包含24和44）
                next_draw = draws[idx + 1]  # 下一期號碼
                
                # 找出當期除了24和44之外的其他號碼
                other_numbers = [num for num in current_draw if num != 24 and num != 44]
                
                # 檢查下一期是否包含最常見的尾數
                next_draw_digits = [n % 10 for n in next_draw]
                has_most_common_digit = most_common_digit[0] in next_draw_digits
                
                for num in other_numbers:
                    if num not in paired_numbers_stats:
                        paired_numbers_stats[num] = {'total': 0, 'with_most_common_digit': 0}
                    
                    paired_numbers_stats[num]['total'] += 1
                    if has_most_common_digit:
                        paired_numbers_stats[num]['with_most_common_digit'] += 1
        
        # 過濾掉出現次數少於2次的號碼
        significant_pairs = {num: stats for num, stats in paired_numbers_stats.items() if stats['total'] >= 2}
        
        if significant_pairs:
            print(f"與24,44同時出現至少2次且對下期尾數{most_common_digit[0]}影響的統計:")
            print("(格式: 號碼: [出現次數, 下期有該尾數次數, 成功率])")
            
            sorted_pairs = sorted(significant_pairs.items(), 
                                 key=lambda x: x[1]['with_most_common_digit']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                 reverse=True)
            
            for num, stats in sorted_pairs:
                success_rate = (stats['with_most_common_digit'] / stats['total']) * 100
                print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_most_common_digit']}, {success_rate:.2f}%]")
            
            # 顯示成功率最高的號碼
            print(f"\n與24,44配對時，下期產生尾數{most_common_digit[0]}機率最高的號碼:")
            top_pairs = [(num, stats) for num, stats in sorted_pairs if stats['with_most_common_digit'] > 0]
            for num, stats in top_pairs[:10]:  # 顯示前10名
                success_rate = (stats['with_most_common_digit'] / stats['total']) * 100
                print(f"  數字 {num:2d}: 機率 {success_rate:.2f}% (共 {stats['total']} 次，成功 {stats['with_most_common_digit']} 次)")
        else:
            print("沒有足夠的數據進行三重號碼分析")

if __name__ == "__main__":
    analyze_number_24_44_pair()
