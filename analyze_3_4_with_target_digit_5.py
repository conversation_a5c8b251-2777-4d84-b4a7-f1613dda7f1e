import csv
from collections import Counter
import os

def analyze_3_4_with_target_digit_5():
    """
    專門分析如何通過添加第三個數字到3和4的配對中來提高尾數5的機率
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找出所有包含3和4的抽獎
    indices_with_3_4 = []
    for i, draw in enumerate(draws):
        if 3 in draw and 4 in draw:
            indices_with_3_4.append(i)
    
    print(f"3和4一起出現的總次數: {len(indices_with_3_4)}")
    
    # 獲取3和4出現後的下一次抽獎數字
    next_draw_numbers_3_4_only = []
    for idx in indices_with_3_4:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers_3_4_only.extend(next_draw)
    
    # 分析僅有3和4時的尾數分布
    last_digits_3_4_only = [num % 10 for num in next_draw_numbers_3_4_only]
    last_digit_counts_3_4_only = Counter(last_digits_3_4_only)
    
    print(f"\n當3和4一起出現時 (29次)，下一次抽獎的尾數分布:")
    for digit in sorted(last_digit_counts_3_4_only.keys()):
        count = last_digit_counts_3_4_only[digit]
        percentage = (count / len(last_digits_3_4_only)) * 10
        print(f"  尾數 {digit}: {count} 次 ({percentage:.2f}%)")
    
    # 專注於尾數5的情況
    digit_5_count_3_4_only = last_digit_counts_3_4_only.get(5, 0)
    digit_5_percentage_3_4_only = (digit_5_count_3_4_only / len(last_digits_3_4_only)) * 10 if len(last_digits_3_4_only) > 0 else 0
    print(f"\n僅有3和4時，尾數5的出現情況:")
    print(f"  出現次數: {digit_5_count_3_4_only} 次")
    print(f"  出現機率: {digit_5_percentage_3_4_only:.2f}%")
    
    # 分析添加不同第三個數字對尾數5機率的影響
    print(f"\n分析添加不同第三個數字到3和4對尾數5機率的影響:")
    print("="*80)
    
    # 存儲結果
    results = []
    
    # 嘗試添加1到49的每個數字來看對尾數5機率的影響
    for third_number in range(1, 50):
        if third_number == 3 or third_number == 4:
            continue  # 跳過3和4，因為它們已經在組合中
        
        # 找出包含3、4和第三個數字的抽獎
        indices_with_3_4_third = []
        for i, draw in enumerate(draws):
            if 3 in draw and 4 in draw and third_number in draw:
                indices_with_3_4_third.append(i)
        
        if len(indices_with_3_4_third) >= 2:  # 只分析至少有2次出現的情況
            # 獲取3、4和第三個數字一起出現後的下一次抽獎數字
            next_draw_numbers_3_4_third = []
            for idx in indices_with_3_4_third:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers_3_4_third.extend(next_draw)
            
            if next_draw_numbers_3_4_third:
                last_digits_3_4_third = [num % 10 for num in next_draw_numbers_3_4_third]
                last_digit_counts_3_4_third = Counter(last_digits_3_4_third)
                
                # 計算尾數5的出現次數和機率
                digit_5_count = last_digit_counts_3_4_third.get(5, 0)
                digit_5_percentage = (digit_5_count / len(last_digits_3_4_third)) * 10 if len(last_digits_3_4_third) > 0 else 0
                
                # 計算相對於僅有3和4時的改善
                improvement = digit_5_percentage - digit_5_percentage_3_4_only
                
                results.append({
                    'third_number': third_number,
                    'occurrences': len(indices_with_3_4_third),
                    'digit_5_count': digit_5_count,
                    'digit_5_percentage': digit_5_percentage,
                    'improvement': improvement
                })
    
    # 按改善程度排序結果
    results.sort(key=lambda x: x['improvement'], reverse=True)
    
    # 印出結果
    print(f"\n尾數5機率改善排名 (原始機率: {digit_5_percentage_3_4_only:.2f}%):")
    print("-" * 80)
    print(f"{'排名':<4} {'第三個數字':<8} {'出現次數':<6} {'尾數5次數':<7} {'尾數5機率':<10} {'改善':<10}")
    print("-" * 80)
    
    for i, result in enumerate(results[:20], 1):  # 只顯示前20個最佳結果
        print(f"{i:<4} {result['third_number']:<8} {result['occurrences']:<6} {result['digit_5_count']:<7} {result['digit_5_percentage']:<10.2f} {result['improvement']:<10.2f}")
    
    # 特別標出顯著改善尾數5機率的組合
    print(f"\n顯著改善尾數5機率的組合 (改善 > 5%):")
    print("-" * 60)
    significant_improvements = [r for r in results if r['improvement'] > 5]
    
    if significant_improvements:
        for result in significant_improvements:
            print(f"  數字 3, 4, {result['third_number']}: {result['digit_5_percentage']:.2f}% (改善 {result['improvement']:.2f}%) - {result['occurrences']} 次出現")
    else:
        print("  沒有找到改善超過5%的組合")
    
    # 找出尾數5機率最高的組合
    print(f"\n尾數5機率最高的組合:")
    print("-" * 60)
    highest_probability = sorted(results, key=lambda x: x['digit_5_percentage'], reverse=True)[:10]
    
    for result in highest_probability:
        print(f"  數字 3, 4, {result['third_number']}: {result['digit_5_percentage']:.2f}% - {result['occurrences']} 次出現")
    
    # 找出在至少5次出現的情況下尾數5機率最高的組合
    print(f"\n至少5次出現且尾數5機率最高的組合:")
    print("-" * 60)
    high_occurrence_results = [r for r in results if r['occurrences'] >= 5]
    if high_occurrence_results:
        high_occurrence_results.sort(key=lambda x: x['digit_5_percentage'], reverse=True)
        for result in high_occurrence_results[:10]:
            print(f"  數字 3, 4, {result['third_number']}: {result['digit_5_percentage']:.2f}% - {result['occurrences']} 次出現")
    else:
        print("  沒有找到至少5次出現的組合")

if __name__ == "__main__":
    analyze_3_4_with_target_digit_5()