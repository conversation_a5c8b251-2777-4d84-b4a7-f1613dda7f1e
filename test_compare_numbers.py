import csv

def load_csv_data(filename):
    """Load number data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer set
            numbers = {int(num.strip()) for num in row}
            data.append(numbers)
    return data

def compare_numbers(input_numbers, csv_data):
    """Compare input numbers with CSV numbers"""
    input_set = set(input_numbers)
    matches = []
    
    for i, row_set in enumerate(csv_data, start=1):
        # Calculate intersection to find common numbers
        common_numbers = input_set.intersection(row_set)
        if common_numbers:
            matches.append({
                'line': i,
                'numbers': sorted(list(common_numbers)),
                'count': len(common_numbers),
                'original_row': sorted(list(row_set))
            })
    
    return matches

def main():
    print("Number comparison program")
    print("Comparing with 'data_compare_lines1.csv' file")
    print("-" * 50)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Test with a known combination that exists in the CSV (first row)
    input_numbers = [13, 21, 23, 27, 31, 49]
    
    print(f"測試號碼: {sorted(input_numbers)}")
    print("-" * 50)
    
    # Check for exact match
    exact_match_line = None
    for i, row_set in enumerate(csv_data, start=1):
        if set(input_numbers) == row_set:
            exact_match_line = i
            break
    
    if exact_match_line:
        print(f"發現完全一樣的號碼組合! 位於第 {exact_match_line} 行")
        print(f"該行號碼: {sorted(list(csv_data[exact_match_line-1]))}")
        print()
    else:
        print("沒有找到完全一樣的號碼組合")
        print()
    
    # Compare numbers for partial matches
    matches = compare_numbers(input_numbers, csv_data)
    
    # Display partial matches
    partial_matches = [match for match in matches if match['count'] < 6]
    if partial_matches:
        print(f"找到 {len(partial_matches)} 筆部分匹配的資料 (顯示前5筆):")
        for i, match in enumerate(partial_matches[:5]):
            print(f"第 {match['line']} 行: 重複號碼 {match['numbers']} (共 {match['count']} 個)")
            print(f"該行號碼: {match['original_row']}")
            print()
        
        if len(partial_matches) > 5:
            print(f"... 還有 {len(partial_matches) - 5} 筆部分匹配資料")
    else:
        print("沒有找到任何部分匹配的資料")
    
    # Show statistics
    print("-" * 50)
    print("統計資訊:")
    
    # Count occurrences of each input number in CSV
    number_counts = {}
    for num in input_numbers:
        count = 0
        for row_set in csv_data:
            if num in row_set:
                count += 1
        number_counts[num] = count
    
    print("輸入號碼在CSV中的出現次數:")
    for num in sorted(input_numbers):
        print(f"  號碼 {num:2d}: {number_counts[num]} 次")

if __name__ == "__main__":
    main()