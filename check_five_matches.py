import csv

def load_csv_data(filename):
    """Load number data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer set
            numbers = {int(num.strip()) for num in row}
            data.append(numbers)
    return data

def compare_numbers(input_numbers, csv_data):
    """Compare input numbers with CSV numbers"""
    input_set = set(input_numbers)
    matches = []
    
    for i, row_set in enumerate(csv_data, start=1):
        # Calculate intersection to find common numbers
        common_numbers = input_set.intersection(row_set)
        if common_numbers:
            matches.append({
                'line': i,
                'numbers': sorted(list(common_numbers)),
                'count': len(common_numbers),
                'original_row': sorted(list(row_set))
            })
    
    return matches

def main():
    print("檢查是否有5個重複號碼的情況")
    print("=" * 50)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"成功載入 {len(csv_data)} 筆號碼資料\n")
    except FileNotFoundError:
        print("錯誤: 找不到 'data_compare_lines1.csv' 檔案")
        return
    except Exception as e:
        print(f"載入檔案時發生錯誤: {e}")
        return
    
    # 檢查所有可能的組合是否有5個重複號碼的情況
    print("正在檢查是否存在5個重複號碼的情況...")
    
    # 找到一些可能有5個重複號碼的例子
    five_match_examples = []
    
    for i, row1 in enumerate(csv_data):
        for j, row2 in enumerate(csv_data[i+1:], i+1):
            common = row1.intersection(row2)
            if len(common) == 5:  # 找到有5個共同號碼的兩行
                five_match_examples.append({
                    'row1_line': i+1,
                    'row1_numbers': sorted(list(row1)),
                    'row2_line': j+1,
                    'row2_numbers': sorted(list(row2)),
                    'common_numbers': sorted(list(common))
                })
                if len(five_match_examples) >= 5:  # 只找前幾個例子
                    break
        if len(five_match_examples) >= 5:
            break
    
    if five_match_examples:
        print(f"\n找到 {len(five_match_examples)} 組有5個重複號碼的例子:")
        for idx, example in enumerate(five_match_examples, 1):
            print(f"\n例子 {idx}:")
            print(f"  第 {example['row1_line']} 行: {example['row1_numbers']}")
            print(f"  第 {example['row2_line']} 行: {example['row2_numbers']}")
            print(f"  重複號碼: {example['common_numbers']}")
    else:
        print("在資料集中沒有找到任何有5個重複號碼的組合")
    
    print("\n測試特定號碼組合是否有5個重複的情況:")
    
    # 測試第一行的號碼，移除其中一個號碼，看是否有其他行包含這5個號碼
    first_row = csv_data[0]  # 第一行 [13, 21, 23, 27, 31, 49]
    print(f"第一行號碼: {sorted(list(first_row))}")
    
    # 測試移除一個號碼後的組合
    test_combinations = []
    first_row_list = sorted(list(first_row))
    
    for i in range(len(first_row_list)):
        # 移除第i個號碼，保留其他5個
        test_combo = [first_row_list[j] for j in range(len(first_row_list)) if j != i]
        test_combinations.append(test_combo)
    
    print("\n測試這些5個號碼的組合:")
    for combo in test_combinations:
        matches = compare_numbers(combo, csv_data)
        five_matches = [m for m in matches if m['count'] == 5 and set(m['numbers']) == set(combo)]
        
        if five_matches:
            print(f"  {combo} -> 找到 {len(five_matches)} 筆匹配:")
            for match in five_matches[:2]:  # 顯示前2筆
                print(f"    第 {match['line']} 行: {match['original_row']}")
        else:
            print(f"  {combo} -> 沒有找到完全匹配的5個號碼")

if __name__ == "__main__":
    main()