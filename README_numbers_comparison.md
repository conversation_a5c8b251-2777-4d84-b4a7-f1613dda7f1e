# 號碼比對程式

這個程式可以比對輸入的六個號碼與 'data_compare_lines1.csv' 檔案中的號碼是否有重複。

## 使用方法

### 方法一：互動式輸入（推薦）
```bash
python compare_numbers.py
```
程式會提示您輸入六個號碼，例如輸入：`13,21,23,27,31,49`

### 方法二：命令行參數
```bash
python compare_numbers.py "13,21,23,27,31,49"
```
這將比對指定的六個號碼。

## 程式功能

1. **載入資料**：從 'data_compare_lines1.csv' 檔案載入所有號碼組合
2. **比對號碼**：將輸入的六個號碼與CSV中的每一行進行比對
3. **顯示結果**：
   - 檢查是否有完全一樣的號碼組合
   - 顯示部分匹配的資料
   - 顯示統計資訊（每個輸入號碼在CSV中的出現次數）

## 輸出說明

- **完全匹配**：顯示是否有與輸入號碼完全相同的組合
- **部分匹配**：顯示與輸入號碼有部分重複的行
- **統計資訊**：顯示每個輸入號碼在整個CSV檔案中的出現頻率

## 檔案說明

- `compare_numbers.py`：主要比對程式
- `data_compare_lines1.csv`：用於比對的號碼資料庫
- `README_numbers_comparison.md`：說明文件