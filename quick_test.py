import csv

def load_csv_data(filename):
    """Load number data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer set
            numbers = {int(num.strip()) for num in row}
            data.append(numbers)
    return data

def compare_numbers(input_numbers, csv_data):
    """Compare input numbers with CSV numbers"""
    input_set = set(input_numbers)
    matches = []
    
    for i, row_set in enumerate(csv_data, start=1):
        # Calculate intersection to find common numbers
        common_numbers = input_set.intersection(row_set)
        if common_numbers:
            matches.append({
                'line': i,
                'numbers': sorted(list(common_numbers)),
                'count': len(common_numbers),
                'original_row': sorted(list(row_set))
            })
    
    return matches

def main():
    print("快速測試 - 號碼比對程式")
    print("=" * 50)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"成功載入 {len(csv_data)} 筆號碼資料\n")
    except FileNotFoundError:
        print("錯誤: 找不到 'data_compare_lines1.csv' 檔案")
        return
    except Exception as e:
        print(f"載入檔案時發生錯誤: {e}")
        return
    
    # Test with sample numbers
    test_numbers = [1, 2, 3, 4, 5, 6]
    print(f"測試號碼: {test_numbers}")
    
    # Compare numbers
    matches = compare_numbers(test_numbers, csv_data)
    
    # Check for exact match
    exact_match_line = None
    for i, row_set in enumerate(csv_data, start=1):
        if set(test_numbers) == row_set:
            exact_match_line = i
            break
    
    if exact_match_line:
        print(f"發現完全一樣的號碼組合! 位於第 {exact_match_line} 行")
        print(f"  該行號碼: {sorted(list(csv_data[exact_match_line-1]))}\n")
    else:
        print("沒有找到完全一樣的號碼組合\n")
    
    # Group matches by count of common numbers
    matches_by_count = {}
    for match in matches:
        count = match['count']
        if count not in matches_by_count:
            matches_by_count[count] = []
        matches_by_count[count].append(match)
    
    # Display grouped matches
    print("比對結果統計:")
    for count in sorted(matches_by_count.keys(), reverse=True):
        count_matches = matches_by_count[count]
        print(f"  有 {len(count_matches)} 筆資料包含 {count} 個重複號碼")
    
    total_matches = len(matches)
    print(f"  總共有 {total_matches} 筆匹配的資料")
    
    print("\n程式功能:")
    print("- 可以快速檢查是否有完全一樣的號碼組合")
    print("- 顯示不同重複數量的統計資訊")
    print("- 支援任意六個號碼的比對")

if __name__ == "__main__":
    main()