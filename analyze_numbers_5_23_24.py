import csv
from collections import Counter

def analyze_numbers_5_23_24():
    # 讀取 CSV 文件
    with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析數據，每行包含用逗號分隔的數字
    all_numbers = []
    for line in lines:
        numbers = [int(num.strip()) for num in line.strip().split(',')]
        all_numbers.extend(numbers)
    
    # 計算特定數字的出現次數
    count_5 = all_numbers.count(5)
    count_23 = all_numbers.count(23)
    count_24 = all_numbers.count(24)
    
    print(f"數字 5 出現了 {count_5} 次")
    print(f"數字 23 出現了 {count_23} 次")
    print(f"數字 24 出現了 {count_24} 次")
    
    # 計算三個數字總共出現的次數
    total_count = count_5 + count_23 + count_24
    print(f"數字 5, 23, 24 總共出現了 {total_count} 次")
    
    # 計算這些數字在所有行中出現的頻率
    total_lines = len(lines)
    print(f"總共有 {total_lines} 行數據")
    
    # 計算包含這些數字的行數
    lines_with_5 = 0
    lines_with_23 = 0
    lines_with_24 = 0
    lines_with_all_three = 0  # 同時包含 5, 23, 24 的行數
    
    for line in lines:
        numbers = [int(num.strip()) for num in line.strip().split(',')]
        has_5 = 5 in numbers
        has_23 = 23 in numbers
        has_24 = 24 in numbers
        
        if has_5:
            lines_with_5 += 1
        if has_23:
            lines_with_23 += 1
        if has_24:
            lines_with_24 += 1
        if has_5 and has_23 and has_24:
            lines_with_all_three += 1
    
    print(f"數字 5 出現在 {lines_with_5} 行中")
    print(f"數字 23 出現在 {lines_with_23} 行中")
    print(f"數字 24 出現在 {lines_with_24} 行中")
    print(f"數字 5, 23, 24 同時出現在 {lines_with_all_three} 行中")
    
    return {
        'count_5': count_5,
        'count_23': count_23,
        'count_24': count_24,
        'total_count': total_count,
        'lines_with_5': lines_with_5,
        'lines_with_23': lines_with_23,
        'lines_with_24': lines_with_24,
        'lines_with_all_three': lines_with_all_three,
        'total_lines': total_lines
    }

if __name__ == "__main__":
    result = analyze_numbers_5_23_24()