# 1到49號碼反向追蹤分析專案

這個專案提供了一個完整的反向追蹤分析系統，用於分析彩票中1到49號碼的出現模式。

## 功能特色

- 分析1到49號碼在歷史數據中的出現模式
- 追蹤每個號碼出現前N期內最常出現的號碼
- 將結果記錄在字典中，便於進一步分析
- 支援自定義回顧期數（預設為7期）

## 檔案結構

- `reverse_tracking_analysis.py` - 主要分析邏輯
- `data_compare_lines1.csv` - 輸入數據文件
- `results.json` - 輸出結果文件（可選）

## 使用方法

1. 確保有包含彩票開獎數據的CSV文件
2. 執行 `python reverse_tracking_analysis.py`
3. 查看分析結果

## 資料結構

分析結果存儲在字典中，格式如下：

```python
{
    1: [
        {'number': 8, 'count': 274, 'percentage': 2.4},
        {'number': 22, 'count': 269, 'percentage': 2.3},
        ...
    ],
    2: [
        {'number': 8, 'count': 279, 'percentage': 2.4},
        ...
    ],
    ...
}