# 十個數字比對程式使用說明

## 概述
這個程式可以讓您輸入十個數字，然後將這些數字組合成所有可能的六個數字組合，並與 `data_compare_lines1.csv` 檔案中的數據進行比對，找出有幾個重複號碼的組合（六個、五個、四個等）。

## 程式功能

### 主要功能
1. **讀取數據**：從 `data_compare_lines1.csv` 檔案讀取所有六個數字的組合
2. **輸入驗證**：確保用戶輸入恰好十個不重複的數字（1-49範圍內）
3. **組合生成**：從十個數字中生成所有可能的六個數字組合（共 210 組）
4. **比對分析**：將每組六個數字與 CSV 檔案中的數據進行比對
5. **結果顯示**：統計並顯示有幾個重複號碼的組合數量

### 組合數量計算
從十個數字中選取六個數字的組合數量為：
- C(10,6) = 10!/(6!×4!) = 210 組

## 程式結構

### 檔案說明
- `compare_ten_numbers.py`：主程式，包含互動式輸入功能
- `test_compare_ten_numbers.py`：測試版本，使用預設數字進行測試

### 主要函數
1. `read_csv_data(filename)` - 讀取 CSV 檔案
2. `get_user_input()` - 獲取並驗證用戶輸入
3. `generate_combinations(numbers, size=6)` - 生成所有組合
4. `compare_combinations(user_combinations, csv_data)` - 比對組合
5. `display_results(results)` - 顯示結果

## 使用方法

### 方法一：互動式使用 (compare_ten_numbers.py)
1. 運行程式：
   ```
   python compare_ten_numbers.py
   ```
2. 按提示輸入十個數字，以逗號分隔，例如：
   ```
   1,5,12,18,23,27,31,35,42,48
   ```
3. 程式會自動進行比對並顯示結果

### 方法二：測試使用 (test_compare_ten_numbers.py)
1. 運行測試程式：
   ```
   python test_compare_ten_numbers.py
   ```
2. 程式會使用預設的十個數字進行測試
3. 如需更改預設數字，請修改程式中的 `user_numbers` 變數

## 結果說明

程式會統計以下類型的匹配：
- **6個重複號碼**：完全匹配 CSV 檔案中的某組數字
- **5個重複號碼**：與 CSV 檔案中的某組數字有 5 個相同
- **4個重複號碼**：與 CSV 檔案中的某組數字有 4 個相同
- **3個重複號碼**：與 CSV 檔案中的某組數字有 3 個相同
- **2個重複號碼**：與 CSV 檔案中的某組數字有 2 個相同
- **1個重複號碼**：與 CSV 檔案中的某組數字有 1 個相同
- **0個重複號碼**：與 CSV 檔案中的所有組合都沒有相同數字

## 注意事項
1. 請確保 `data_compare_lines1.csv` 檔案存在且格式正確
2. 輸入的數字必須在 1 到 49 的範圍內
3. 不允許輸入重複的數字
4. 程式會自動尋找每組與 CSV 檔案中最高的匹配數量