import json

def load_results():
    """載入 results.json 檔案"""
    with open('results.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_numbers():
    """分析提供的數字組合與 results.json 中高頻率號碼的匹配度"""
    # 提供的數字組合
    provided_combinations = [
        [11,16,20,31,39,41],
        [3,15,16,31,32,45],
        [14,20,25,42,44,49],
        [13,22,23,33,39,40],
        [5,8,11,12,18,39],
        [3,4,7,10,18,21]
    ]
    
    # 載入 results.json
    results = load_results()
    
    print("Analysis of provided number combinations with high-frequency numbers:")
    print("="*70)
    
    # 為每個位置（1-49）建立高頻率號碼列表
    high_frequency_numbers = {}
    for pos in range(1, 50):
        position_key = str(pos)
        if position_key in results:
            # 取得該位置前幾個高頻率號碼
            high_freq_list = [item['number'] for item in results[position_key][:5]]  # 前5個高頻號碼
            high_frequency_numbers[pos] = high_freq_list
    
    # 分析每個提供的數字組合
    for i, combination in enumerate(provided_combinations):
        print(f"\nCombination {i+1}: {combination}")
        matches = []
        
        for num in combination:
            if num in high_frequency_numbers:
                # 檢查此數字是否出現在其他位置的高頻列表中
                for pos, high_freq_list in high_frequency_numbers.items():
                    if num in high_freq_list:
                        matches.append({
                            'number': num,
                            'position': pos,
                            'rank_in_position': high_freq_list.index(num) + 1  # 排名（1是最高的）
                        })
        
        print(f"  Matching high-frequency numbers: {len(matches)}")
        for match in matches:
            print(f"    Number {match['number']} appears in position {match['position']} high-frequency list (Rank {match['rank_in_position']})")
    
    # 分析每個號碼在所有組合中出現的頻率
    all_numbers = []
    for combination in provided_combinations:
        all_numbers.extend(combination)
    
    number_frequency = {}
    for num in all_numbers:
        number_frequency[num] = number_frequency.get(num, 0) + 1
    
    print(f"\nMost frequently appearing numbers in all combinations:")
    sorted_numbers = sorted(number_frequency.items(), key=lambda x: x[1], reverse=True)
    for num, freq in sorted_numbers[:10]:  # 顯示前10個
        print(f"  Number {num}: appears {freq} times")
    
    # 分析是否號碼49會出現
    print(f"\nAnalyzing the possibility of number 49 appearing:")
    # 檢查號碼49在results.json中作為高頻號碼的位置
    positions_with_49 = []
    for pos in range(1, 50):
        position_key = str(pos)
        if position_key in results:
            high_freq_list = [item['number'] for item in results[position_key][:10]]  # 前10個高頻號碼
            if 49 in high_freq_list:
                rank_49 = high_freq_list.index(49) + 1
                positions_with_49.append((pos, rank_49))
    
    print(f"Number 49 appears in the following positions' high-frequency lists:")
    for pos, rank in positions_with_49:
        print(f"  Position {pos}: Rank {rank}")
    
    # 檢查提供的數字組合中是否包含49
    combinations_with_49 = []
    for i, combination in enumerate(provided_combinations):
        if 49 in combination:
            combinations_with_49.append(i+1)
    
    print(f"\nProvided number combinations containing number 49:")
    if combinations_with_49:
        for combo_idx in combinations_with_49:
            print(f" Combination {combo_idx}: {provided_combinations[combo_idx-1]}")
    else:
        print("  No combinations contain number 49")
    
    # 根據高頻號碼分析預測可能出現的號碼
    print(f"\nPrediction analysis based on high-frequency numbers:")
    predictions = {}
    
    for pos in range(1, 7):  # 分析前6個位置（因為彩票通常是6個號碼）
        position_key = str(pos)
        if position_key in results:
            # 取得該位置最常出現的號碼
            top_numbers = results[position_key][:5]
            predictions[pos] = top_numbers
    
    print("Most frequently appearing numbers for each position (top 5):")
    for pos, top_numbers in predictions.items():
        print(f"  Position {pos}: {[num_info['number'] for num_info in top_numbers]}")
        
        # 檢查這些高頻號碼是否出現在提供的組合中
        matching_input_numbers = []
        for num_info in top_numbers:
            num = num_info['number']
            for i, combination in enumerate(provided_combinations):
                if num in combination:
                    matching_input_numbers.append({
                        'number': num,
                        'combination': i+1,
                        'combination_numbers': combination
                    })
        
        if matching_input_numbers:
            print(f"    These numbers also appear in provided combinations:")
            for match in matching_input_numbers:
                print(f"      Number {match['number']} appears in combination {match['combination']}")

if __name__ == "__main__":
    analyze_numbers()