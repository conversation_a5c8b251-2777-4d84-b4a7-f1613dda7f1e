import csv
from io import String<PERSON>
from itertools import combinations
from collections import Counter, defaultdict


def parse_csv_data(csv_content):
    """
    Parses CSV content into a list of lists of integers.
    Each inner list represents a line of numbers.
    """
    data = []
    # Use StringIO to treat the string content as a file
    csvfile = StringIO(csv_content)
    reader = csv.reader(csvfile)
    for row in reader:
        # Convert each number string to an integer
        data.append([int(num) for num in row])
    return data


def analyze_reverse_tracking_49_numbers(all_draws, lookback_period=7):
    """
    分析 1 到 49 號碼的反向追蹤 - 前N期內最常出現的號碼
    :param all_draws: 所有開獎結果
    :param lookback_period: 回顧期數，預設為7期
    :return: 包含分析結果的字典
    """
    results = {}
    
    if len(all_draws) > lookback_period:
        # 計算1到49每個號碼在歷史中出現後，前N期內最常出現的號碼
        number_cooccurrence = defaultdict(lambda: Counter())
        for num in range(1, 50):
            for idx, draw in enumerate(all_draws[lookback_period:], lookback_period):  # 從第(lookback_period+1)期開始，確保前N期存在
                if num in draw:
                    # 統計前N期內出現的號碼
                    for prev_idx in range(idx-lookback_period, idx):
                        for prev_num in all_draws[prev_idx]:
                            number_cooccurrence[num][prev_num] += 1
        
        # 將每個號碼在前N期內最常出現的伴隨號碼記錄到結果字典中
        for num in range(1, 50):
            if num in number_cooccurrence:
                top_5 = number_cooccurrence[num].most_common(5)
                results[num] = []
                for assoc_num, count in top_5:
                    percentage = count / sum(number_cooccurrence[num].values()) * 100
                    results[num].append({
                        'number': assoc_num,
                        'count': count,
                        'percentage': round(percentage, 1)
                    })
            else:
                results[num] = []  # 該號碼在歷史中未出現過
    
    return results


def print_reverse_tracking_results(results, lookback_period=7):
    """
    打印反向追蹤分析結果
    :param results: 分析結果字典
    :param lookback_period: 回顧期數
    """
    print(f"\n=== 1到49號碼反向追蹤分析 - 前{lookback_period}期內最常出現的號碼 ===")
    
    for num in range(1, 50):
        if results[num]:  # 如果該號碼有記錄
            print(f"\n號碼 {num} 出現前{lookback_period}期內最常出現的號碼:")
            for item in results[num]:
                print(f" 號碼 {item['number']}: {item['count']} 次 ({item['percentage']}%)")
        else:
            print(f"\n號碼 {num} 在歷史中未出現過")


def main():
    try:
        with open('data_compare_lines1.csv', 'r') as f:
            csv_content = f.read()
    except FileNotFoundError:
        print("Error: data_compare_lines1.csv not found.")
        return

    all_draws = parse_csv_data(csv_content)
    
    # 執行反向追蹤分析
    results = analyze_reverse_tracking_49_numbers(all_draws, lookback_period=7)
    
    # 打印結果
    print_reverse_tracking_results(results, lookback_period=7)
    
    # 顯示結果字典的結構
    print(f"\n分析完成！結果已記錄在字典中，包含 {len(results)} 個號碼的追蹤資料")
    print("字典結構範例：results[1] =", results[1][:2])  # 顯示號碼1的前2筆記錄


if __name__ == "__main__":
    main()