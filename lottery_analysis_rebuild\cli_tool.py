#!/usr/bin/env python3
"""彩券分析CLI工具"""

import argparse
import sys
from pathlib import Path
import matplotlib.pyplot as plt

# 添加項目路徑到系統路徑
sys.path.append(str(Path(__file__).parent))

from config import Config
from data_loader import load_csv_data, validate_input_numbers
from analyzer import LotteryAnalyzer, MatchResult
from advanced_analyzer import AdvancedLotteryAnalyzer
# 模擬 __version__ 變數
__version__ = "1.0.0"


def create_parser():
    """創建命令列解析器"""
    parser = argparse.ArgumentParser(
        prog='lottery-analyzer',
        description='彩券號碼分析CLI工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例:
  lottery-analyzer compare --numbers "1,2,3,4,5,6,7,8"
  lottery-analyzer compare --numbers "1,2,3,4,5,6,7,8" --file "data_compare_lines2.csv"
lottery-analyzer analyze --file "data_compare_lines1.csv"
lottery-analyzer visualize --file "data_compare_lines1.csv" --output "analysis.png"
  lottery-analyzer stats --numbers "1,2,3,4,5,6,7,8"
        """
    )
    parser.add_argument('--version', '-v', action='version', version=f'彩券分析工具 {__version__}')
    
    subparsers = parser.add_subparsers(dest='command', help='可用的命令', required=True)
    
    # 比較命令
    compare_parser = subparsers.add_parser('compare', help='比較號碼')
    compare_parser.add_argument('--numbers', '-n', required=True, 
                               help='要比較的號碼 (以逗號分隔，例如: "1,2,3,4,5,6,7,8")')
    compare_parser.add_argument('--file', '-f', default='data_compare_lines1.csv',
                               help='CSV檔案路徑 (預設: data_compare_lines1.csv)')
    compare_parser.add_argument('--min-match', '-m', type=int, default=3,
                               help='最小匹配數量 (預設: 3)')
    
    # 分析命令
    analyze_parser = subparsers.add_parser('analyze', help='進階分析')
    analyze_parser.add_argument('--file', '-f', default='data_compare_lines1.csv',
                               help='CSV檔案路徑 (預設: data_compare_lines1.csv)')
    
    # 視覺化命令
    visualize_parser = subparsers.add_parser('visualize', help='生成視覺化圖表')
    visualize_parser.add_argument('--file', '-f', default='data_compare_lines1.csv',
                                 help='CSV檔案路徑 (預設: data_compare_lines1.csv)')
    visualize_parser.add_argument('--output', '-o', default='lottery_analysis.png',
                                 help='輸出檔案名稱 (預設: lottery_analysis.png)')
    
    # 統計命令
    stats_parser = subparsers.add_parser('stats', help='號碼統計')
    stats_parser.add_argument('--numbers', '-n', required=True,
                             help='要統計的號碼 (以逗號分隔)')
    stats_parser.add_argument('--file', '-f', default='data_compare_lines1.csv',
                             help='CSV檔案路徑 (預設: data_compare_lines1.csv)')
    
    # 批次比較命令
    batch_parser = subparsers.add_parser('batch', help='批次比較多組號碼')
    batch_parser.add_argument('--numbers-list', '-l', required=True,
                             help='多組號碼 (以分號分隔，例如: "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16")')
    batch_parser.add_argument('--file', '-f', default='data_compare_lines1.csv',
                             help='CSV檔案路徑 (預設: data_compare_lines1.csv)')
    
    return parser


def parse_numbers(numbers_str):
    """解析號碼字串"""
    try:
        numbers = [int(x.strip()) for x in numbers_str.split(',')]
        return numbers
    except ValueError:
        raise ValueError(f"無效的號碼格式: {numbers_str}")


def parse_numbers_list(numbers_list_str):
    """解析號碼列表字串"""
    groups = []
    for group_str in numbers_list_str.split(';'):
        numbers = parse_numbers(group_str.strip())
        groups.append(numbers)
    return groups


def cmd_compare(args):
    """比較號碼命令"""
    try:
        numbers = parse_numbers(args.numbers)
        analyzer = LotteryAnalyzer(args.file)
        
        print(f"載入資料: {len(analyzer.data)} 行")
        print(f"比較號碼: {sorted(numbers)}")
        print("-" * 50)
        
        results = analyzer.compare_numbers(numbers, args.min_match)
        
        # 檢查完全匹配
        exact_match = analyzer.find_exact_match(numbers)
        if exact_match:
            print(f"O 發現完全匹配! 位於第 {exact_match} 行")
            print(f"  該行號碼: {sorted(list(analyzer.data[exact_match-1]))}")
        else:
            print("X 沒有找到完全匹配")
        
        # 顯示部分匹配
        total_matches = sum(len(matches) for matches in results.values())
        if total_matches > 0:
            print(f"\n找到 {total_matches} 筆部分匹配的資料:")
            for count in range(7, args.min_match - 1, -1):
                matches = results.get(count, [])
                if matches:
                    print(f"  {count} 個重複號碼: {len(matches)} 筆")
                    # 顯示前5筆詳細資訊
                    for match in matches[:5]:
                        print(f"    第 {match.line} 行: {match.numbers}")
                    if len(matches) > 5:
                        print(f"    ... 還有 {len(matches) - 5} 筆")
        else:
            print("\n沒有找到任何部分匹配的資料")
        
        # 顯示統計
        stats = analyzer.get_number_statistics(numbers)
        print(f"\n輸入號碼在CSV中的出現次數:")
        for num in sorted(numbers):
            print(f"  號碼 {num:2d}: {stats[num]} 次")
            
    except ValueError as e:
        print(f"錯誤: {e}")
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
    except Exception as e:
        print(f"錯誤: {e}")


def cmd_analyze(args):
    """進階分析命令"""
    try:
        analyzer = AdvancedLotteryAnalyzer(args.file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print("-" * 50)
        
        report = analyzer.generate_detailed_report()
        
        print("數據總覽:")
        print(f"  總行數: {report['total_rows']}")
        print(f" 總數字數: {report['total_numbers']}")
        print(f" 唯一數字數: {report['unique_numbers']}")
        print(f"  數字範圍: {report['number_range'][0]} - {report['number_range'][1]}")
        
        print("\n出現頻率最高的10個數字:")
        for num, freq in report['top_numbers'][:10]:
            print(f" 號碼 {num:2d}: {freq} 次")
        
        print("\n出現頻率最低的10個數字:")
        for num, freq in report['lowest_numbers'][:10]:
            print(f" 號碼 {num:2d}: {freq} 次")
        
        print("\n各位置最常出現的數字:")
        for pos_idx, pos_data in enumerate(report['position_distribution']):
            print(f" 第{pos_idx+1}位置:")
            for num, freq in pos_data[:3]: # 顯示前3個
                print(f"    號碼 {num:2d}: {freq} 次")
        
        print("\n數字範圍分布:")
        for range_name, count in report['range_distribution'].items():
            percentage = (count / report['total_numbers']) * 10 if report['total_numbers'] > 0 else 0
            print(f"  {range_name}: {count} 個數字 ({percentage:.1f}%)")
        
        print(f"\n和值統計:")
        print(f"  最小和值: {report['sum_stats']['min']:.0f}")
        print(f" 最大和值: {report['sum_stats']['max']:.0f}")
        print(f" 平均和值: {report['sum_stats']['avg']:.2f}")
        
        print("\n奇偶數分布:")
        for (odd, even), count in report['odd_even_distribution'][:10]:
            print(f" {odd}個奇數/{even}個偶數: {count} 次")
            
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
    except Exception as e:
        print(f"錯誤: {e}")


def cmd_visualize(args):
    """視覺化命令"""
    try:
        analyzer = AdvancedLotteryAnalyzer(args.file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print(f"生成視覺化圖表: {args.output}")
        
        analyzer.create_visualization(args.output)
        print(f"O 視覺化圖表已儲存至: {args.output}")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
    except Exception as e:
        print(f"錯誤: {e}")


def cmd_stats(args):
    """統計命令"""
    try:
        numbers = parse_numbers(args.numbers)
        analyzer = LotteryAnalyzer(args.file)
        
        print(f"統計號碼: {sorted(numbers)}")
        print("-" * 50)
        
        # 獲取統計資訊
        stats = analyzer.get_number_statistics(numbers)
        
        print("各號碼在CSV中的出現次數:")
        for num in sorted(numbers):
            print(f"  號碼 {num:2d}: {stats[num]} 次")
        
        # 計算總體統計
        total_appearances = sum(stats.values())
        avg_appearances = total_appearances / len(numbers) if numbers else 0
        
        print(f"\n總出現次數: {total_appearances}")
        print(f"平均出現次數: {avg_appearances:.2f}")
        
        # 找出出現最多和最少的號碼
        if stats:
            most_frequent = max(stats.items(), key=lambda x: x[1])
            least_frequent = min(stats.items(), key=lambda x: x[1])
            print(f"出現最多: 號碼 {most_frequent[0]} ({most_frequent[1]} 次)")
            print(f"出現最少: 號碼 {least_frequent[0]} ({least_frequent[1]} 次)")
            
    except ValueError as e:
        print(f"錯誤: {e}")
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
    except Exception as e:
        print(f"錯誤: {e}")


def cmd_batch(args):
    """批次比較命令"""
    try:
        groups = parse_numbers_list(args.numbers_list)
        analyzer = LotteryAnalyzer(args.file)
        
        print(f"載入資料: {len(analyzer.data)} 行")
        print(f"批次比較 {len(groups)} 組號碼")
        print("-" * 50)
        
        for i, numbers in enumerate(groups, 1):
            print(f"\n第 {i} 組號碼: {sorted(numbers)}")
            
            # 執行比較
            results = analyzer.compare_numbers(numbers)
            
            # 檢查完全匹配
            exact_match = analyzer.find_exact_match(numbers)
            if exact_match:
                print(f"  O 完全匹配: 第 {exact_match} 行")
            else:
                print(f"  X 無完全匹配")
            
            # 計算總匹配數
            total_matches = sum(len(matches) for matches in results.values())
            print(f"  總匹配數: {total_matches}")
            
            # 顯示各類匹配數量
            match_summary = {}
            for count, matches in results.items():
                if matches:
                    match_summary[count] = len(matches)
            
            if match_summary:
                summary_str = ", ".join([f"{c}個重複:{n}筆" for c, n in sorted(match_summary.items(), reverse=True)])
                print(f"  摘要: {summary_str}")
            else:
                print(f"  摘要: 無匹配")
        
    except ValueError as e:
        print(f"錯誤: {e}")
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
    except Exception as e:
        print(f"錯誤: {e}")


def main():
    """主函數"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 根據命令執行相應功能
    if args.command == 'compare':
        cmd_compare(args)
    elif args.command == 'analyze':
        cmd_analyze(args)
    elif args.command == 'visualize':
        cmd_visualize(args)
    elif args.command == 'stats':
        cmd_stats(args)
    elif args.command == 'batch':
        cmd_batch(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()