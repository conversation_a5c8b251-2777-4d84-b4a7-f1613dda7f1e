#!/usr/bin/env python3
"""彩券分析工具主執行檔"""

import sys
import argparse
from lottery_analysis_rebuild import LotteryAnalyzer, AdvancedLotteryAnalyzer


def main():
    parser = argparse.ArgumentParser(description='彩券號碼分析工具')
    parser.add_argument('--file', '-f', default='../data_compare_lines1.csv', 
                       help='CSV檔案路徑 (預設: ../data_compare_lines1.csv)')
    parser.add_argument('--numbers', '-n', type=str, 
                       help='要分析的號碼 (以逗號分隔)')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='互動模式')
    parser.add_argument('--advanced', '-a', action='store_true',
                       help='顯示進階分析')
    parser.add_argument('--visualize', '-v', action='store_true',
                       help='生成視覺化圖表')
    
    args = parser.parse_args()
    
    print("彩券號碼分析工具")
    print("="*50)
    
    # 建立分析器
    try:
        if args.advanced:
            analyzer = AdvancedLotteryAnalyzer(args.file)
        else:
            analyzer = LotteryAnalyzer(args.file)
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {args.file}")
        sys.exit(1)
    except Exception as e:
        print(f"載入檔案時發生錯誤: {e}")
        sys.exit(1)
    
    print(f"已載入 {len(analyzer.data)} 行數據")
    
    # 獲取號碼
    if args.numbers:
        try:
            input_numbers = [int(x.strip()) for x in args.numbers.split(',')]
        except ValueError:
            print("錯誤: 請提供有效的數字，以逗號分隔")
            sys.exit(1)
    elif args.interactive:
        while True:
            try:
                user_input = input("請輸入8個號碼 (以逗號分隔): ")
                input_numbers = [int(x.strip()) for x in user_input.split(',')]
                
                if len(input_numbers) != 8:
                    print(f"請輸入剛好8個號碼! 您輸入了{len(input_numbers)}個")
                    continue
                
                if len(set(input_numbers)) != 8:
                    print("號碼不能重複!")
                    continue
                
                break
            except ValueError:
                print("請輸入有效的數字，以逗號分隔!")
    else:
        # 使用預設測試號碼
        input_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
        print(f"使用預設號碼: {input_numbers}")
    
    print(f"分析號碼: {sorted(input_numbers)}")
    print("-" * 50)
    
    if args.advanced:
        # 進階分析
        print("執行進階分析...")
        report = analyzer.generate_detailed_report()
        
        print("數據總覽:")
        print(f" 總行數: {report['total_rows']}")
        print(f"  總數字數: {report['total_numbers']}")
        print(f" 唯一數字數: {report['unique_numbers']}")
        print(f"  數字範圍: {report['number_range'][0]} - {report['number_range'][1]}")
        
        print("\n出現頻率最高的10個數字:")
        for num, freq in report['top_numbers'][:10]:
            print(f" 號碼 {num}: {freq} 次")
        
        print("\n數字範圍分布:")
        for range_name, count in report['range_distribution'].items():
            print(f"  {range_name}: {count} 個數字")
        
        print(f"\n和值統計:")
        print(f"  最小和值: {report['sum_stats']['min']:.0f}")
        print(f" 最大和值: {report['sum_stats']['max']:.0f}")
        print(f"  平均和值: {report['sum_stats']['avg']:.2f}")
    
    else:
        # 基本比較
        results = analyzer.compare_numbers(input_numbers)
        
        # 檢查完全匹配
        exact_match = analyzer.find_exact_match(input_numbers)
        if exact_match:
            print(f"發現完全匹配! 位於第 {exact_match} 行")
        else:
            print("沒有找到完全匹配")
        
        # 顯示部分匹配
        total_matches = sum(len(matches) for matches in results.values())
        if total_matches > 0:
            print(f"\n找到 {total_matches} 筆部分匹配的資料:")
            
            from lottery_analysis_rebuild.config import Config
            for count in range(7, Config.DEFAULT_MIN_MATCH_COUNT - 1, -1):
                matches = results.get(count, [])
                if matches:
                    print(f"  {count} 個重複號碼: {len(matches)} 筆")
        
        # 顯示統計
        stats = analyzer.get_number_statistics(input_numbers)
        print("\n輸入號碼在CSV中的出現次數:")
        for num in sorted(input_numbers):
            print(f"  號碼 {num:2d}: {stats[num]} 次")
    
    # 生成視覺化圖表
    if args.visualize and args.advanced:
        try:
            output_file = 'lottery_analysis_output.png'
            analyzer.create_visualization(output_file)
            print(f"\n視覺化圖表已儲存至: {output_file}")
        except Exception as e:
            print(f"生成視覺化圖表時發生錯誤: {e}")


if __name__ == "__main__":
    main()