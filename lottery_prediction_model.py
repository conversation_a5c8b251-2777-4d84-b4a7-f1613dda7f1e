import json
from collections import Counter, defaultdict

def load_results():
    """Load results.json file"""
    with open('results.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_prediction_model():
    """分析預測模型 - 比較提供的數字與高頻率號碼的匹配度"""
    # 提供的數字組合
    provided_combinations = [
        [11,16,20,31,39,41],
        [3,15,16,31,32,45],
        [14,20,25,42,44,49],
        [13,22,23,33,39,40],
        [5,8,11,12,18,39],
        [3,4,7,10,18,21]
    ]
    
    # 載入 results.json
    results = load_results()
    
    print("彩票預測模型分析 - 匹配度比較")
    print("="*40)
    
    # 統計每個號碼在提供的組合中出現的次數
    number_appearances = Counter()
    for combination in provided_combinations:
        for num in combination:
            number_appearances[num] += 1
    
    print(f"\n1. 提供組合中號碼出現次數：")
    sorted_appearances = sorted(number_appearances.items(), key=lambda x: x[1], reverse=True)
    for num, count in sorted_appearances:
        print(f"  號碼 {num}: 出現 {count} 次")
    
    # 找出在多個組合中出現的號碼（出現3次或以上）
    frequent_in_combinations = {num for num, count in number_appearances.items() if count >= 3}
    print(f"\n2. 在3個或以上組合中出現的號碼：{sorted(frequent_in_combinations)}")
    
    # 檢查這些號碼在各個位置的高頻率排名
    print(f"\n3. 這些號碼在各位置的高頻率排名：")
    position_analysis = {}
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            top_10_numbers = [item['number'] for item in results[pos_key][:10]]
            position_analysis[pos] = {
                'top_10': top_10_numbers,
                'matching_frequent': []  # 在多組中出現且在該位置高頻的號碼
            }
            
            for num in frequent_in_combinations:
                if num in top_10_numbers:
                    rank = top_10_numbers.index(num) + 1
                    position_analysis[pos]['matching_frequent'].append((num, rank))
    
    for pos, data in position_analysis.items():
        print(f"  位置 {pos}:")
        if data['matching_frequent']:
            for num, rank in data['matching_frequent']:
                print(f"    號碼 {num} 在此位置排名第 {rank}")
        else:
            print(f"    沒有在多組中出現的號碼在此位置是高頻號碼")
    
    # 分析號碼49的情況
    print(f"\n4. 號碼49詳細分析：")
    print(f"  在提供的組合中出現：{'是' if 49 in number_appearances else '否'}")
    if 49 in number_appearances:
        print(f"  出現在第 {number_appearances[49]} 次")
    
    # 檢查49在各位置的排名
    positions_with_49 = []
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            top_20_numbers = [item['number'] for item in results[pos_key][:20]]
            if 49 in top_20_numbers:
                rank = top_20_numbers.index(49) + 1
                positions_with_49.append((pos, rank))
    
    if positions_with_49:
        print(f"  在以下位置排名前20：")
        for pos, rank in positions_with_49:
            print(f"    位置 {pos}: 排名 {rank}")
    else:
        print(f"  在前6個位置中都不在前20名高頻號碼")
    
    # 基於匹配度的預測
    print(f"\n5. 基於匹配度的預測：")
    
    # 方法1: 選擇在多組中出現且在該位置排名最高的號碼
    predicted_by_frequency = []
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            top_10_numbers = [item['number'] for item in results[pos_key][:10]]
            
            # 找出同時在多組中出現且在此位置高頻的號碼
            candidates = []
            for num in top_10_numbers:
                if num in frequent_in_combinations:
                    rank_in_pos = top_10_numbers.index(num) + 1
                    appearance_count = number_appearances[num]
                    # 計算綜合得分（位置排名 + 出現次數）
                    score = (11 - rank_in_pos) + appearance_count  # 排名越前分數越高，出現次數越多分數越高
                    candidates.append((num, score, rank_in_pos, appearance_count))
            
            if candidates:
                # 選擇得分最高的號碼
                best_candidate = max(candidates, key=lambda x: x[1])
                predicted_by_frequency.append(best_candidate[0])
                print(f" 位置 {pos}: 預測 {best_candidate[0]} (得分: {best_candidate[1]}, 位置排名: {best_candidate[2]}, 出現次數: {best_candidate[3]})")
            else:
                # 如果沒有同時滿足條件的號碼，則選擇在該位置排名最高的
                best_num = top_10_numbers[0]
                predicted_by_frequency.append(best_num)
                print(f"  位置 {pos}: 預測 {best_num} (該位置最高頻)")
    
    print(f"\n方法1預測結果: {predicted_by_frequency}")
    
    # 方法2: 基於整體高頻率號碼和出現次數的平衡預測
    print(f"\n6. 平衡預測方法：")
    
    # 為每個位置創建預測
    balanced_predictions = []
    used_numbers = set()  # 追蹤已使用的號碼，避免重複
    
    for pos in range(1, 7):
        pos_key = str(pos)
        if pos_key in results:
            # 取得該位置的前20高頻號碼
            top_20_numbers = [item['number'] for item in results[pos_key][:20]]
            
            # 計算每個號碼的綜合評分
            scored_numbers = []
            for num in top_20_numbers:
                if num in used_numbers:
                    continue  # 避免選到重複號碼
                
                pos_rank = top_20_numbers.index(num) + 1
                appearance_count = number_appearances.get(num, 0)
                
                # 計算評分：位置排名分數 + 出現次數分數
                pos_score = 21 - pos_rank  # 排名1 = 20分，排名20 = 1分
                appearance_score = appearance_count * 5  # 每次出現給5分
                total_score = pos_score + appearance_score
                
                scored_numbers.append((num, total_score, pos_rank, appearance_count))
            
            if scored_numbers:
                # 選擇評分最高的號碼
                best_choice = max(scored_numbers, key=lambda x: x[1])
                balanced_predictions.append(best_choice[0])
                used_numbers.add(best_choice[0])
                print(f" 位置 {pos}: 預測 {best_choice[0]} (評分: {best_choice[1]}, 位置排名: {best_choice[2]}, 出現次數: {best_choice[3]})")
            else:
                # 如果所有高頻號碼都已被選用，則從前30名中選擇一個未使用的
                top_30_numbers = [item['number'] for item in results[pos_key][:30]]
                for num in top_30_numbers:
                    if num not in used_numbers:
                        balanced_predictions.append(num)
                        used_numbers.add(num)
                        pos_rank = top_30_numbers.index(num) + 1
                        appearance_count = number_appearances.get(num, 0)
                        print(f"  位置 {pos}: 預測 {num} (評分: 優先選擇未重複號碼, 位置排名: {pos_rank}, 出現次數: {appearance_count})")
                        break
    
    print(f"\n平衡預測結果: {balanced_predictions}")
    
    # 對49號的最終評估
    print(f"\n7. 號碼49最終評估：")
    
    # 檢查49在所有位置的排名
    all_positions_with_49 = []
    for pos in range(1, 50):  # 檢查所有位置
        pos_key = str(pos)
        if pos_key in results:
            top_10_numbers = [item['number'] for item in results[pos_key][:10]]
            if 49 in top_10_numbers:
                rank = top_10_numbers.index(49) + 1
                all_positions_with_49.append((pos, rank))
    
    print(f"  在所有位置中，49號在 {len(all_positions_with_49)} 個位置排名前10")
    if all_positions_with_49:
        print(f"  出現在位置: {[pos for pos, rank in all_positions_with_49]}")
        avg_rank = sum(rank for pos, rank in all_positions_with_49) / len(all_positions_with_49)
        print(f"  平均排名: {avg_rank:.1f}")
    
    # 基於整體分析的最終預測
    final_prediction = balanced_predictions
    print(f"\n8. 最終預測結果: {final_prediction}")
    
    # 分析預測的合理性
    print(f"\n9. 預測結果分析：")
    total_frequency_score = 0
    total_appearance_score = 0
    
    for i, pred_num in enumerate(final_prediction):
        pos = i + 1
        pos_key = str(pos)
        if pos_key in results:
            top_numbers = results[pos_key]
            pos_rank = next((idx for idx, item in enumerate(top_numbers) if item['number'] == pred_num), None)
            if pos_rank is not None:
                pos_percentage = top_numbers[pos_rank]['percentage']
                appearance_count = number_appearances.get(pred_num, 0)
                print(f"  位置 {pos} 號碼 {pred_num}: 排名 {pos_rank+1}, 出現率 {pos_percentage}%, 在提供組合中出現 {appearance_count} 次")
                total_frequency_score += (100 - pos_rank) # 排名越前分數越高
                total_appearance_score += appearance_count
    
    print(f"\n總體評估：")
    print(f" 頻率適應性得分: {total_frequency_score}")
    print(f"  組合匹配度得分: {total_appearance_score}")
    print(f"  號碼49出現可能性: {'較高' if 49 in final_prediction else '較低'}")
    
    return final_prediction

if __name__ == "__main__":
    final_prediction = analyze_prediction_model()