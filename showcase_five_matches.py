import csv

def load_csv_data(filename):
    """Load number data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer set
            numbers = {int(num.strip()) for num in row}
            data.append(numbers)
    return data

def compare_numbers(input_numbers, csv_data):
    """Compare input numbers with CSV numbers"""
    input_set = set(input_numbers)
    matches = []
    
    for i, row_set in enumerate(csv_data, start=1):
        # Calculate intersection to find common numbers
        common_numbers = input_set.intersection(row_set)
        if common_numbers:
            matches.append({
                'line': i,
                'numbers': sorted(list(common_numbers)),
                'count': len(common_numbers),
                'original_row': sorted(list(row_set))
            })
    
    return matches

def main():
    print("展示5個重複號碼的範例")
    print("=" * 50)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"成功載入 {len(csv_data)} 筆號碼資料\n")
    except FileNotFoundError:
        print("錯誤: 找不到 'data_compare_lines1.csv' 檔案")
        return
    except Exception as e:
        print(f"載入檔案時發生錯誤: {e}")
        return
    
    # 檢查所有可能的組合是否有5個重複號碼的情況
    print("正在尋找5個重複號碼的範例...")
    
    five_match_examples = []
    
    for i, row1 in enumerate(csv_data):
        for j, row2 in enumerate(csv_data[i+1:], i+1):
            common = row1.intersection(row2)
            if len(common) == 5:  # 找到有5個共同號碼的兩行
                five_match_examples.append({
                    'row1_line': i+1,
                    'row1_numbers': sorted(list(row1)),
                    'row2_line': j+1,
                    'row2_numbers': sorted(list(row2)),
                    'common_numbers': sorted(list(common)),
                    'unique_row1': sorted(list(row1 - row2)),  # row1獨有的號碼
                    'unique_row2': sorted(list(row2 - row1))   # row2獨有的號碼
                })
                if len(five_match_examples) >= 5:  # 只找前幾個例子
                    break
        if len(five_match_examples) >= 5:
            break
    
    if five_match_examples:
        print(f"\n找到 {len(five_match_examples)} 組有5個重複號碼的例子:")
        for idx, example in enumerate(five_match_examples, 1):
            print(f"\n範例 {idx}:")
            print(f"  第 {example['row1_line']:4d} 行: {example['row1_numbers']}")
            print(f"  第 {example['row2_line']:4d} 行: {example['row2_numbers']}")
            print(f"  重複號碼: {example['common_numbers']}")
            print(f"  第一行獨有: [{example['unique_row1'][0]}]  vs  第二行獨有: [{example['unique_row2'][0]}]")
            
            # 測試其中一行作為輸入，看是否能找到5個重複號碼
            test_input = example['row1_numbers']
            matches = compare_numbers(test_input, csv_data)
            five_matches = [m for m in matches if m['count'] == 5 and set(m['numbers']) == set(example['common_numbers'])]
            
            if five_matches:
                print(f"  → 使用第 {example['row1_line']} 行作為輸入時，可找到第 {five_matches[0]['line']} 行有5個重複號碼")
    else:
        print("在資料集中沒有找到任何有5個重複號碼的組合")
    
    print("\n程式可以正確識別並顯示5個重複號碼的情況!")

if __name__ == "__main__":
    main()