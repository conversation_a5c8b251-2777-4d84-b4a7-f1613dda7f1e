import csv
from itertools import combinations

def load_csv_data(filename):
    """Load all data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer list
            numbers = [int(num.strip()) for num in row]
            data.append(numbers)
    return data

def find_all_5_number_matches(csv_data):
    """Find all pairs of rows that have exactly 5 common numbers"""
    matches = []
    
    for i in range(len(csv_data)):
        for j in range(i + 1, len(csv_data)):  # j > i, so comparing later rows with earlier rows
            row1 = set(csv_data[i])
            row2 = set(csv_data[j])
            common_numbers = row1.intersection(row2)
            
            if len(common_numbers) == 5:
                matches.append({
                    'line1': i + 1,  # earlier line
                    'line2': j + 1,  # later line
                    'row1': sorted(csv_data[i]),
                    'row2': sorted(csv_data[j]),
                    'common_numbers': sorted(list(common_numbers))
                })
    
    return matches

def find_reverse_logic_matches(csv_data):
    """Find matches using the same logic as reverse_compare_numbers.py"""
    matches = []
    
    # Process each line starting from the end, going to the beginning
    for i in range(len(csv_data) - 1, -1, -1):  # from last line to first line
        current_line = csv_data[i]
        current_line_number = i + 1
        
        # Compare with lines that come before it (with smaller index)
        for j in range(i - 1, -1, -1):  # Compare with previous lines
            compare_line = csv_data[j]
            common_numbers = set(current_line).intersection(set(compare_line))
            
            if len(common_numbers) == 5:
                matches.append({
                    'line1': j + 1,  # earlier line
                    'line2': current_line_number,  # later line (current line)
                    'row1': sorted(compare_line),
                    'row2': sorted(current_line),
                    'common_numbers': sorted(list(common_numbers))
                })
    
    return matches

def main():
    print("Comparing different approaches to find 5-number matches...")
    print("=" * 70)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Find all matches with 5 common numbers (comparing each line with all later lines)
    all_matches = find_all_5_number_matches(csv_data)
    
    # Find matches using reverse logic (from last entry backwards to beginning)
    reverse_matches = find_reverse_logic_matches(csv_data)
    
    print(f"Total pairs with 5 common numbers (all comparisons): {len(all_matches)}")
    print(f"Total pairs with 5 common numbers (reverse logic): {len(reverse_matches)}")
    print()
    
    # Check if they are the same
    print("Checking if both methods produce the same results...")
    
    # Convert matches to sets of tuples for comparison
    all_match_tuples = set()
    for match in all_matches:
        match_tuple = (match['line1'], match['line2'], tuple(match['common_numbers']))
        all_match_tuples.add(match_tuple)
    
    reverse_match_tuples = set()
    for match in reverse_matches:
        match_tuple = (match['line1'], match['line2'], tuple(match['common_numbers']))
        reverse_match_tuples.add(match_tuple)
    if all_match_tuples == reverse_match_tuples:
        print("O Both methods produce the same results!")
    else:
        print("X Methods produce different results")
        print(f"All method found {len(all_match_tuples)} unique matches")
        print(f"Reverse method found {len(reverse_match_tuples)} unique matches")
        
        
        only_in_all = all_match_tuples - reverse_match_tuples
        only_in_reverse = reverse_match_tuples - all_match_tuples
        
        if only_in_all:
            print(f"\nMatches only in 'all' method ({len(only_in_all)}):")
            for match in only_in_all:
                print(f"  Lines {match[0]}, {match[1]} with common numbers {list(match[2])}")
        
        if only_in_reverse:
            print(f"\nMatches only in 'reverse' method ({len(only_in_reverse)}):")
            for match in only_in_reverse:
                print(f"  Lines {match[0]}, {match[1]} with common numbers {list(match[2])}")
    
    print("\n" + "=" * 70)
    print("Summary:")
    print(f"Total pairs with 5 common numbers: {len(all_matches)}")
    
    # Show all matches found by reverse method (the original program)
    print("\nAll matches found by reverse_compare_numbers.py logic:")
    for match in reverse_matches:
        print(f"Line {match['line2']}: {match['row2']} 對應行 {match['line1']}: {match['row1']} 含有 5 個重複的數字 {match['common_numbers']}")

if __name__ == "__main__":
    main()