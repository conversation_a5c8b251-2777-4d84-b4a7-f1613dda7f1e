"""彩券分析工具使用範例"""

from lottery_analysis_rebuild import LotteryAnalyzer, AdvancedLotteryAnalyzer
import sys
import os

# 確保能讀取到CSV檔案
csv_file_path = '../data_compare_lines1.csv'

def basic_usage_example():
    """基本使用範例"""
    print("=" * 60)
    print("基本號碼比較範例")
    print("=" * 60)
    
    # 建立分析器
    analyzer = LotteryAnalyzer(csv_file_path)
    print(f"已載入 {len(analyzer.data)} 行數據")
    
    # 測試號碼比較
    test_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
    print(f"測試號碼: {test_numbers}")
    
    # 執行比較
    results = analyzer.compare_numbers(test_numbers)
    
    # 顯示結果
    total_matches = sum(len(matches) for matches in results.values())
    print(f"找到 {total_matches} 筆匹配資料:")
    
    for count in range(7, 2, -1):  # 從7到3
        matches = results.get(count, [])
        if matches:
            print(f"  {count} 個重複號碼: {len(matches)} 筆")
            # 顯示前3筆詳細資訊
            for match in matches[:3]:
                print(f"    第 {match.line} 行: {match.numbers} (原行: {match.original_row})")
            if len(matches) > 3:
                print(f"    ... 還有 {len(matches) - 3} 筆")
    
    # 檢查完全匹配
    exact_match = analyzer.find_exact_match(test_numbers)
    if exact_match:
        print(f"完全匹配行號: {exact_match}")
    else:
        print("沒有找到完全匹配")
    
    # 顯示統計
    stats = analyzer.get_number_statistics(test_numbers)
    print("各號碼出現次數:")
    for num in sorted(stats.keys()):
        print(f"  號碼 {num}: {stats[num]} 次")


def advanced_usage_example():
    """進階分析範例"""
    print("\n" + "=" * 60)
    print("進階分析範例")
    print("=" * 60)
    
    # 建立進階分析器
    advanced_analyzer = AdvancedLotteryAnalyzer(csv_file_path)
    
    # 生成詳細報告
    report = advanced_analyzer.generate_detailed_report()
    
    print("數據總覽:")
    print(f" 總行數: {report['total_rows']}")
    print(f"  總數字數: {report['total_numbers']}")
    print(f"  唯一數字數: {report['unique_numbers']}")
    print(f"  數字範圍: {report['number_range'][0]} - {report['number_range'][1]}")
    
    print("\n出現頻率最高的10個數字:")
    for num, freq in report['top_numbers']:
        print(f"  號碼 {num}: {freq} 次")
    
    print("\n各位置最常出現的數字:")
    for pos_idx, pos_data in enumerate(report['position_distribution']):
        print(f"  第{pos_idx+1}位置:")
        for num, freq in pos_data[:3]:  # 顯示前3個
            print(f"    號碼 {num}: {freq} 次")
    
    print("\n數字範圍分布:")
    for range_name, count in report['range_distribution'].items():
        print(f"  {range_name}: {count} 個數字")
    
    print(f"\n和值統計:")
    print(f"  最小和值: {report['sum_stats']['min']:.0f}")
    print(f" 最大和值: {report['sum_stats']['max']:.0f}")
    print(f"  平均和值: {report['sum_stats']['avg']:.2f}")
    
    print("\n奇偶數分布 (奇數數量:偶數數量 -> 出現次數):")
    for (odd, even), count in report['odd_even_distribution'][:10]:
        print(f" {odd}個奇數/{even}個偶數: {count} 次")


def interactive_mode():
    """互動模式"""
    print("\n" + "=" * 60)
    print("互動模式 - 請輸入您的號碼")
    print("=" * 60)
    
    analyzer = LotteryAnalyzer(csv_file_path)
    
    while True:
        try:
            user_input = input("\n請輸入8個號碼 (以逗號分隔，或輸入 'q' 離開): ")
            if user_input.lower() == 'q':
                break
                
            numbers = [int(x.strip()) for x in user_input.split(',')]
            
            if len(numbers) != 8:
                print(f"請輸入剛好8個號碼! 您輸入了{len(numbers)}個")
                continue
            
            if len(set(numbers)) != 8:
                print("號碼不能重複!")
                continue
            
            print(f"分析號碼: {sorted(numbers)}")
            
            # 執行比較
            results = analyzer.compare_numbers(numbers)
            
            # 顯示結果
            total_matches = sum(len(matches) for matches in results.values())
            if total_matches > 0:
                print(f"找到 {total_matches} 筆匹配資料:")
                for count in range(7, 2, -1):
                    matches = results.get(count, [])
                    if matches:
                        print(f"  {count} 個重複號碼: {len(matches)} 筆")
            else:
                print("沒有找到任何匹配資料")
                
        except ValueError:
            print("請輸入有效的數字，以逗號分隔!")
        except KeyboardInterrupt:
            print("\n程式被中斷")
            break


def run_examples():
    """執行所有範例"""
    print("彩券分析工具使用範例")
    print(f"資料檔案: {csv_file_path}")
    
    # 檢查檔案是否存在
    if not os.path.exists(csv_file_path):
        print(f"錯誤: 找不到檔案 {csv_file_path}")
        print("請確保 data_compare_lines1.csv 檔案存在於專案根目錄")
        return
    
    try:
        basic_usage_example()
        advanced_usage_example()
        
        # 問詢是否要進入互動模式
        response = input("\n是否要進入互動模式? (y/n): ")
        if response.lower() in ['y', 'yes', '是']:
            interactive_mode()
            
    except Exception as e:
        print(f"執行範例時發生錯誤: {e}")


if __name__ == "__main__":
    run_examples()