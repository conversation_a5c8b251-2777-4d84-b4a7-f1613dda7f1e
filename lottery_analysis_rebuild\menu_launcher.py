#!/usr/bin/env python3
"""彩券分析工具啟動選單"""

import sys
import os
from pathlib import Path

# 添加項目路徑到系統路徑
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from config import Config
from data_loader import load_csv_data, validate_input_numbers
from analyzer import LotteryAnalyzer, MatchResult
from advanced_analyzer import AdvancedLotteryAnalyzer


def clear_screen():
    """清除螢幕"""
    os.system('cls' if os.name == 'nt' else 'clear')


def print_header():
    """印出標頭"""
    print("=" * 60)
    print("           歡迎使用彩券分析工具")
    print("           Lottery Analysis Tool")
    print("=" * 60)


def print_menu():
    """印出選單"""
    print("\n請選擇功能:")
    print("1. 號碼比較 - 比較您的號碼與資料庫")
    print("2. 進階分析 - 全面分析資料庫趨勢")
    print("3. 視覺化圖表 - 生成數據圖表")
    print("4. 號碼統計 - 查看特定號碼統計")
    print("5. 批次比較 - 一次比較多組號碼")
    print("6. 互動模式 - 輕鬆互動式分析")
    print("0. 離開")
    print("-" * 60)


def get_csv_file():
    """取得CSV檔案路徑"""
    default_file = 'data_compare_lines1.csv'
    print(f"\n預設檔案: {default_file}")
    choice = input("是否使用預設檔案? (Y/n): ").strip().lower()
    
    if choice in ['n', 'no']:
        file_path = input("請輸入CSV檔案路徑: ").strip()
        if not file_path:
            return default_file
        return file_path
    else:
        return default_file


def get_numbers_input(prompt="請輸入號碼 (以逗號分隔): ", expected_count=8):
    """取得號碼輸入"""
    while True:
        try:
            user_input = input(prompt)
            numbers = [int(x.strip()) for x in user_input.split(',')]
            
            if len(numbers) != expected_count:
                print(f"請輸入剛好{expected_count}個號碼! 您輸入了{len(numbers)}個")
                continue
            
            if len(set(numbers)) != expected_count:
                print("號碼不能重複!")
                continue
            
            # 檢查號碼範圍
            if any(num < 1 or num > 49 for num in numbers):
                print("號碼必須在1到49之間!")
                continue
            
            return numbers
        except ValueError:
            print("請輸入有效的數字，以逗號分隔!")


def get_multiple_number_groups():
    """取得多組號碼輸入"""
    groups = []
    print("請輸入多組號碼 (每組8個，以分號分隔各組)")
    print("例如: 1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16")
    
    while True:
        try:
            user_input = input("請輸入多組號碼: ")
            groups_str = user_input.split(';')
            
            for group_str in groups_str:
                numbers = [int(x.strip()) for x in group_str.split(',')]
                if len(numbers) != 8:
                    print(f"每組必須包含8個號碼! '{group_str.strip()}' 包含{len(numbers)}個號碼")
                    raise ValueError
                if len(set(numbers)) != 8:
                    print(f"組內號碼不能重複! '{group_str.strip()}' 包含重複號碼")
                    raise ValueError
                groups.append(numbers)
            
            return groups
        except ValueError:
            print("請輸入有效的號碼組合，格式: 1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16")


def option_1_compare():
    """選項1: 號碼比較"""
    print("\n--- 號碼比較 ---")
    csv_file = get_csv_file()
    
    try:
        analyzer = LotteryAnalyzer(csv_file)
        print(f"載入資料: {len(analyzer.data)} 行")
        
        numbers = get_numbers_input()
        print(f"比較號碼: {sorted(numbers)}")
        print("-" * 50)
        
        results = analyzer.compare_numbers(numbers)
        
        # 檢查完全匹配
        exact_match = analyzer.find_exact_match(numbers)
        if exact_match:
            print(f"✓ 發現完全匹配! 位於第 {exact_match} 行")
            print(f"  該行號碼: {sorted(list(analyzer.data[exact_match-1]))}")
        else:
            print("✗ 沒有找到完全匹配")
        
        # 顯示部分匹配
        total_matches = sum(len(matches) for matches in results.values())
        if total_matches > 0:
            print(f"\n找到 {total_matches} 筆部分匹配的資料:")
            for count in range(7, 2, -1):
                matches = results.get(count, [])
                if matches:
                    print(f"  {count} 個重複號碼: {len(matches)} 筆")
                    # 顯示前5筆詳細資訊
                    for match in matches[:5]:
                        print(f"    第 {match.line} 行: {match.numbers}")
                    if len(matches) > 5:
                        print(f"    ... 還有 {len(matches) - 5} 筆")
        else:
            print("\n沒有找到任何部分匹配的資料")
        
        # 顯示統計
        stats = analyzer.get_number_statistics(numbers)
        print(f"\n輸入號碼在CSV中的出現次數:")
        for num in sorted(numbers):
            print(f"  號碼 {num:2d}: {stats[num]} 次")
        
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def option_2_analyze():
    """選項2: 進階分析"""
    print("\n--- 進階分析 ---")
    csv_file = get_csv_file()
    
    try:
        analyzer = AdvancedLotteryAnalyzer(csv_file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print("-" * 50)
        
        report = analyzer.generate_detailed_report()
        
        print("📊 數據總覽:")
        print(f" 總行數: {report['total_rows']}")
        print(f" 總數字數: {report['total_numbers']}")
        print(f"  唯一數字數: {report['unique_numbers']}")
        print(f"  數字範圍: {report['number_range'][0]} - {report['number_range'][1]}")
        
        print("\n📈 出現頻率最高的10個數字:")
        for num, freq in report['top_numbers'][:10]:
            print(f" 號碼 {num:2d}: {freq} 次")
        
        print("\n📉 出現頻率最低的10個數字:")
        for num, freq in report['lowest_numbers'][:10]:
            print(f" 號碼 {num:2d}: {freq} 次")
        
        print("\n📊 各位置最常出現的數字:")
        for pos_idx, pos_data in enumerate(report['position_distribution']):
            print(f" 第{pos_idx+1}位置:")
            for num, freq in pos_data[:3]:  # 顯示前3個
                print(f"    號碼 {num:2d}: {freq} 次")
        
        print("\n🔢 數字範圍分布:")
        for range_name, count in report['range_distribution'].items():
            percentage = (count / report['total_numbers']) * 10 if report['total_numbers'] > 0 else 0
            print(f"  {range_name}: {count} 個數字 ({percentage:.1f}%)")
        
        print(f"\n📊 和值統計:")
        print(f"  最小和值: {report['sum_stats']['min']:.0f}")
        print(f" 最大和值: {report['sum_stats']['max']:.0f}")
        print(f"  平均和值: {report['sum_stats']['avg']:.2f}")
        
        print("\n🔢 奇偶數分布:")
        for (odd, even), count in report['odd_even_distribution'][:10]:
            print(f" {odd}個奇數/{even}個偶數: {count} 次")
        
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def option_3_visualize():
    """選項3: 視覺化圖表"""
    print("\n--- 視覺化圖表 ---")
    csv_file = get_csv_file()
    
    output_file = input("請輸出圖檔名稱 (預設: lottery_analysis.png): ").strip()
    if not output_file:
        output_file = 'lottery_analysis.png'
    
    try:
        analyzer = AdvancedLotteryAnalyzer(csv_file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print(f"生成視覺化圖表: {output_file}")
        
        analyzer.create_visualization(output_file)
        print(f"✓ 視覺化圖表已儲存至: {output_file}")
        
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def option_4_stats():
    """選項4: 號碼統計"""
    print("\n--- 號碼統計 ---")
    csv_file = get_csv_file()
    
    try:
        analyzer = LotteryAnalyzer(csv_file)
        numbers = get_numbers_input()
        
        print(f"統計號碼: {sorted(numbers)}")
        print("-" * 50)
        
        # 獲取統計資訊
        stats = analyzer.get_number_statistics(numbers)
        
        print("各號碼在CSV中的出現次數:")
        for num in sorted(numbers):
            print(f"  號碼 {num:2d}: {stats[num]} 次")
        
        # 計算總體統計
        total_appearances = sum(stats.values())
        avg_appearances = total_appearances / len(numbers) if numbers else 0
        
        print(f"\n總出現次數: {total_appearances}")
        print(f"平均出現次數: {avg_appearances:.2f}")
        
        # 找出出現最多和最少的號碼
        if stats:
            most_frequent = max(stats.items(), key=lambda x: x[1])
            least_frequent = min(stats.items(), key=lambda x: x[1])
            print(f"出現最多: 號碼 {most_frequent[0]} ({most_frequent[1]} 次)")
            print(f"出現最少: 號碼 {least_frequent[0]} ({least_frequent[1]} 次)")
        
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def option_5_batch():
    """選項5: 批次比較"""
    print("\n--- 批次比較 ---")
    csv_file = get_csv_file()
    
    try:
        analyzer = LotteryAnalyzer(csv_file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print("-" * 50)
        
        groups = get_multiple_number_groups()
        print(f"批次比較 {len(groups)} 組號碼")
        print("-" * 50)
        
        for i, numbers in enumerate(groups, 1):
            print(f"\n第 {i} 組號碼: {sorted(numbers)}")
            
            # 執行比較
            results = analyzer.compare_numbers(numbers)
            
            # 檢查完全匹配
            exact_match = analyzer.find_exact_match(numbers)
            if exact_match:
                print(f"  ✓ 完全匹配: 第 {exact_match} 行")
            else:
                print(f"  ✗ 無完全匹配")
            
            # 計算總匹配數
            total_matches = sum(len(matches) for matches in results.values())
            print(f"  總匹配數: {total_matches}")
            
            # 顯示各類匹配數量
            match_summary = {}
            for count, matches in results.items():
                if matches:
                    match_summary[count] = len(matches)
            
            if match_summary:
                summary_str = ", ".join([f"{c}個重複:{n}筆" for c, n in sorted(match_summary.items(), reverse=True)])
                print(f"  摘要: {summary_str}")
            else:
                print(f"  摘要: 無匹配")
        
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def option_6_interactive():
    """選項6: 互動模式"""
    print("\n--- 互動模式 ---")
    csv_file = get_csv_file()
    
    try:
        analyzer = LotteryAnalyzer(csv_file)
        print(f"載入資料: {len(analyzer.data)} 行")
        print("進入互動模式 - 輸入 'q' 或 'quit' 離開")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n請輸入8個號碼 (以逗號分隔，或輸入 'q' 離開): ")
                if user_input.lower() in ['q', 'quit', 'exit']:
                    break
                
                numbers = [int(x.strip()) for x in user_input.split(',')]
                
                if len(numbers) != 8:
                    print(f"請輸入剛好8個號碼! 您輸入了{len(numbers)}個")
                    continue
                
                if len(set(numbers)) != 8:
                    print("號碼不能重複!")
                    continue
                
                print(f"分析號碼: {sorted(numbers)}")
                
                # 執行比較
                results = analyzer.compare_numbers(numbers)
                
                # 顯示結果摘要
                total_matches = sum(len(matches) for matches in results.values())
                if total_matches > 0:
                    print(f"找到 {total_matches} 筆匹配資料:")
                    for count in range(7, 2, -1):
                        matches = results.get(count, [])
                        if matches:
                            print(f" {count} 個重複號碼: {len(matches)} 筆")
                else:
                    print("沒有找到任何匹配資料")
                    
            except ValueError:
                print("請輸入有效的數字，以逗號分隔!")
            except KeyboardInterrupt:
                print("\n程式被中斷")
                break
        
        print("離開互動模式")
        input("\n按 Enter 鍵返回主選單...")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {csv_file}")
        input("按 Enter 鍵返回...")
    except Exception as e:
        print(f"錯誤: {e}")
        input("按 Enter 鍵返回...")


def main():
    """主函數"""
    while True:
        clear_screen()
        print_header()
        print_menu()
        
        try:
            choice = input("請選擇選項 (0-6): ").strip()
            
            if choice == '1':
                option_1_compare()
            elif choice == '2':
                option_2_analyze()
            elif choice == '3':
                option_3_visualize()
            elif choice == '4':
                option_4_stats()
            elif choice == '5':
                option_5_batch()
            elif choice == '6':
                option_6_interactive()
            elif choice == '0':
                print("\n感謝使用彩券分析工具，再見！")
                break
            else:
                print("\n無效的選項，請選擇 0-6 之間的數字")
                input("按 Enter 鍵繼續...")
                
        except KeyboardInterrupt:
            print("\n\n程式被中斷，再見！")
            break
        except Exception as e:
            print(f"\n發生錯誤: {e}")
            input("按 Enter 鍵繼續...")


if __name__ == "__main__":
    main()