# CLI 工具使用說明

## 安裝

要將 CLI 工具安裝為系統指令，請執行：

```bash
pip install -e .
```

或直接執行腳本：

```bash
python cli_tool.py [命令] [選項]
```

## 命令列表

### 1. compare - 比較號碼
比較指定號碼與CSV檔案中的資料

```bash
python cli_tool.py compare --numbers "1,2,3,4,5,6,7,8"
```

**選項:**
- `--numbers, -n` (必需): 要比較的號碼 (以逗號分隔)
- `--file, -f`: CSV檔案路徑 (預設: ../data_compare_lines1.csv)
- `--min-match, -m`: 最小匹配數量 (預設: 3)

**範例:**
```bash
# 基本比較
python cli_tool.py compare --numbers "1,2,3,4,5,6,7,8"

# 指定檔案和最小匹配數量
python cli_tool.py compare --numbers "1,2,3,4,5,6,7,8" --file "../data_compare_lines2.csv" --min-match 4
```

### 2. analyze - 進階分析
對CSV檔案進行全面分析

```bash
python cli_tool.py analyze
```

**選項:**
- `--file, -f`: CSV檔案路徑 (預設: ../data_compare_lines1.csv)

**範例:**
```bash
# 分析預設檔案
python cli_tool.py analyze

# 分析指定檔案
python cli_tool.py analyze --file "../data_compare_lines2.csv"
```

### 3. visualize - 生成視覺化圖表
生成數據的視覺化圖表

```bash
python cli_tool.py visualize
```

**選項:**
- `--file, -f`: CSV檔案路徑 (預設: ../data_compare_lines1.csv)
- `--output, -o`: 輸出檔案名稱 (預設: lottery_analysis.png)

**範例:**
```bash
# 生成預設圖表
python cli_tool.py visualize

# 指定輸出檔案名稱
python cli_tool.py visualize --output "my_analysis.png"
```

### 4. stats - 號碼統計
統計指定號碼在CSV中的出現情況

```bash
python cli_tool.py stats --numbers "1,2,3,4,5,6,7,8"
```

**選項:**
- `--numbers, -n` (必需): 要統計的號碼 (以逗號分隔)
- `--file, -f`: CSV檔案路徑 (預設: ../data_compare_lines1.csv)

**範例:**
```bash
# 統計號碼出現次數
python cli_tool.py stats --numbers "1,2,3,4,5,6,7,8"

# 使用不同檔案
python cli_tool.py stats --numbers "1,2,3,4,5,6,7,8" --file "../data_compare_lines2.csv"
```

### 5. batch - 批次比較
批次比較多組號碼

```bash
python cli_tool.py batch --numbers-list "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16"
```

**選項:**
- `--numbers-list, -l` (必需): 多組號碼 (以分號分隔)
- `--file, -f`: CSV檔案路徑 (預設: ../data_compare_lines1.csv)

**範例:**
```bash
# 批次比較兩組號碼
python cli_tool.py batch --numbers-list "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16"

# 使用不同檔案
python cli_tool.py batch --numbers-list "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16" --file "../data_compare_lines2.csv"
```

## 完整範例

```bash
# 1. 比較號碼
python cli_tool.py compare -n "1,2,3,4,5,6,7,8" -f "../data_compare_lines1.csv"

# 2. 進行進階分析
python cli_tool.py analyze -f "../data_compare_lines1.csv"

# 3. 生成視覺化圖表
python cli_tool.py visualize -f "../data_compare_lines1.csv" -o "analysis.png"

# 4. 統計號碼
python cli_tool.py stats -n "1,2,3,4,5,6,7,8" -f "../data_compare_lines1.csv"

# 5. 批次比較
python cli_tool.py batch -l "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16;17,18,19,20,21,22,23,24" -f "../data_compare_lines1.csv"
```

## 系統指令安裝

安裝後可直接使用 `lottery-analyzer` 指令：

```bash
# 安裝
pip install -e .

# 使用系統指令
lottery-analyzer compare -n "1,2,3,4,5,6,7,8"
lottery-analyzer analyze
lottery-analyzer visualize -o "chart.png"
```

## 版本資訊

查看版本：
```bash
python cli_tool.py --version