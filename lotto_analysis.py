import pandas as pd
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
from itertools import combinations

# 讀取數據
def load_data(file_path):
    data = []
    with open(file_path, 'r') as f:
        for line in f:
            numbers = list(map(int, line.strip().split(',')))
            data.append(numbers)
    return data

# 分析數字頻率
def analyze_frequency(data):
    all_numbers = [num for sublist in data for num in sublist]
    freq = Counter(all_numbers)
    return freq

# 分析數字對共現頻率
def analyze_pairs(data):
    pair_counts = Counter()
    for draw in data:
        for pair in combinations(sorted(draw), 2):
            pair_counts[pair] += 1
    return pair_counts

# 預測下一期數字
def predict_next_numbers(data, top_n=10):
    # 計算數字頻率
    freq = analyze_frequency(data)
    
    # 計算數字對共現頻率
    pair_counts = analyze_pairs(data)
    
    # 獲取最常出現的數字
    most_common = [num for num, _ in freq.most_common()]
    
    # 獲取最常一起出現的數字對
    most_common_pairs = [pair for pair, _ in pair_counts.most_common(20)]
    
    # 從最常出現的數字中選擇
    predicted = most_common[:top_n]
    
    # 添加一些來自最常見數字對的數字
    for pair in most_common_pairs:
        for num in pair:
            if num not in predicted and len(predicted) < 15:  # 最多15個預測數字
                predicted.append(num)
    
    return sorted(list(set(predicted)))

# 主函數
def main():
    file_path = 'data_compare_lines1.csv'
    data = load_data(file_path)
    
    # 分析數據
    freq = analyze_frequency(data)
    
    print("=== 樂透號碼分析報告 ===")
    print(f"總共分析了 {len(data)} 期開獎結果")
    
    # 顯示最常出現的數字
    print("\n最常出現的數字（熱號）：")
    for num, count in freq.most_common(10):
        print(f"數字 {num}: 出現 {count} 次 ({(count/len(data))*100:.1f}%)")
    
    # 顯示最少出現的數字
    print("\n最少出現的數字（冷號）：")
    for num, count in freq.most_common()[-10:]:
        print(f"數字 {num}: 出現 {count} 次 ({(count/len(data))*100:.1f}%)")
    
    # 預測下一期可能出現的數字
    predicted = predict_next_numbers(data)
    print(f"\n預測下一期可能出現的數字（共{len(predicted)}個）：")
    print(sorted(predicted))
    
    # 可視化
    plt.figure(figsize=(12, 6))
    numbers = list(range(1, 50))
    counts = [freq.get(num, 0) for num in numbers]
    
    plt.bar(numbers, counts)
    plt.title('樂透號碼出現頻率分佈')
    plt.xlabel('號碼')
    plt.ylabel('出現次數')
    plt.xticks(range(1, 50, 2))
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 保存圖表
    plt.tight_layout()
    plt.savefig('lotto_frequency.png')
    print("\n已生成號碼頻率分佈圖: lotto_frequency.png")

if __name__ == "__main__":
    main()
