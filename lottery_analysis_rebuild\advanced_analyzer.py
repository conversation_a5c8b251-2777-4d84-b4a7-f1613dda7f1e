"""進階彩券分析器"""

from collections import Counter
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Dict, Tu<PERSON>
from analyzer import LotteryAnalyzer
from config import Config


class AdvancedLotteryAnalyzer(LotteryAnalyzer):
    """進階彩券分析器，繼承基本分析器功能"""
    
    def analyze_frequency(self) -> Counter:
        """分析數字出現頻率"""
        all_numbers = [num for row_set in self.data for num in row_set]
        return Counter(all_numbers)
    
    def analyze_position_distribution(self) -> List[Counter]:
        """分析數字在各位置的分布"""
        # 將集合轉換為有序列表以進行位置分析
        all_rows = []
        for row_set in self.data:
            all_rows.append(sorted(list(row_set)))
        
        position_counts = [Counter() for _ in range(6)]  # 6個位置
        
        for row in all_rows:
            for pos, num in enumerate(row):
                position_counts[pos][num] += 1
        
        return position_counts
    
    def analyze_range_distribution(self) -> Dict[str, int]:
        """分析數字範圍分布"""
        ranges = {
            '1-10': 0,
            '11-20': 0,
            '21-30': 0,
            '31-40': 0,
            '41-49': 0
        }
        
        all_numbers = [num for row_set in self.data for num in row_set]
        
        for num in all_numbers:
            if 1 <= num <= 10:
                ranges['1-10'] += 1
            elif 11 <= num <= 20:
                ranges['11-20'] += 1
            elif 21 <= num <= 30:
                ranges['21-30'] += 1
            elif 31 <= num <= 40:
                ranges['31-40'] += 1
            elif 41 <= num <= 49:
                ranges['41-49'] += 1
        
        return ranges
    
    def analyze_sum_distribution(self) -> List[int]:
        """分析數字和值的分布"""
        # 將集合轉換為列表計算和值
        sums = []
        for row_set in self.data:
            row_list = sorted(list(row_set))
            sums.append(sum(row_list))
        return sums
    
    def analyze_odd_even_distribution(self) -> List[Tuple[int, int]]:
        """分析奇偶數分布"""
        odd_even_stats = []
        
        for row_set in self.data:
            row_list = sorted(list(row_set))
            odd_count = sum(1 for num in row_list if num % 2 == 1)
            even_count = len(row_list) - odd_count
            odd_even_stats.append((odd_count, even_count))
        
        return odd_even_stats
    
    def generate_detailed_report(self) -> Dict:
        """生成詳細分析報告"""
        frequency_counter = self.analyze_frequency()
        position_counts = self.analyze_position_distribution()
        range_dist = self.analyze_range_distribution()
        sums = self.analyze_sum_distribution()
        odd_even_stats = self.analyze_odd_even_distribution()
        
        # 頻率最高的10個數字
        sorted_freq = sorted(frequency_counter.items(), key=lambda x: x[1], reverse=True)
        top_numbers = sorted_freq[:10]
        
        # 頻率最低的10個數字
        lowest_numbers = sorted_freq[-10:]
        
        # 各位置最常出現的數字
        pos_top_numbers = []
        for pos in range(6):
            pos_sorted = sorted(position_counts[pos].items(), key=lambda x: x[1], reverse=True)[:5]
            pos_top_numbers.append(pos_sorted)
        
        # 奇偶數分布統計
        from collections import Counter as CollectionsCounter
        odd_even_counter = CollectionsCounter(odd_even_stats)
        
        return {
            'total_rows': len(self.data),
            'total_numbers': len(self.data) * 6,
            'unique_numbers': len(frequency_counter),
            'number_range': (min(frequency_counter.keys()), max(frequency_counter.keys())),
            'top_numbers': top_numbers,
            'lowest_numbers': lowest_numbers,
            'position_distribution': pos_top_numbers,
            'range_distribution': range_dist,
            'sum_stats': {
                'min': min(sums),
                'max': max(sums),
                'avg': sum(sums) / len(sums)
            },
            'odd_even_distribution': odd_even_counter.most_common()
        }
    
    def create_visualization(self, output_file: str = 'lottery_analysis.png'):
        """創建數據可視化圖表"""
        frequency_counter = self.analyze_frequency()
        range_dist = self.analyze_range_distribution()
        sums = self.analyze_sum_distribution()
        odd_even_stats = self.analyze_odd_even_distribution()
        
        sorted_freq = sorted(frequency_counter.items(), key=lambda x: x[0])  # 按數字排序
        numbers, frequencies = zip(*sorted_freq) if sorted_freq else ([], [])
        
        plt.figure(figsize=(15, 10))
        
        # 頻率分布圖
        plt.subplot(2, 2, 1)
        if numbers and frequencies:
            plt.bar(numbers, frequencies)
        plt.title('數字出現頻率分布')
        plt.xlabel('數字')
        plt.ylabel('出現次數')
        
        # 範圍分布圖
        plt.subplot(2, 2, 2)
        range_names = list(range_dist.keys())
        range_counts = list(range_dist.values())
        plt.bar(range_names, range_counts)
        plt.title('數字範圍分布')
        plt.xlabel('範圍')
        plt.ylabel('數字總數')
        
        # 和值分布圖
        plt.subplot(2, 2, 3)
        if sums:
            plt.hist(sums, bins=30, edgecolor='black')
        plt.title('和值分布')
        plt.xlabel('和值')
        plt.ylabel('出現次數')
        
        # 奇偶數分布圖
        plt.subplot(2, 2, 4)
        if odd_even_stats:
            odd_counts = [odd for odd, even in odd_even_stats]
            plt.hist(odd_counts, bins=range(8), edgecolor='black', align='left')
        plt.title('每行奇數數量分布')
        plt.xlabel('奇數數量')
        plt.ylabel('出現次數')
        plt.xticks(range(7))
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()