import csv
from collections import Counter
import os

def find_two_number_must_appear_extended():
    """
    尋找兩個數字組合出現後，下一期一定會出現的數字 (擴大搜索範圍)
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("尋找兩個數字組合後下一期一定會出現的數字 (擴大搜索範圍)...")
    print("="*80)
    
    # 測試所有可能的兩個數字組合 (擴大範圍)
    combinations_found = []
    
    for first_num in range(1, 50):
        for second_num in range(first_num+1, 50):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 檢查出現2次或以上的情況
            if len(indices_with_pair) >= 2:
                # 獲取下一期的所有數字
                next_draws_list = []
                
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draws_list.append(next_draw)
                
                if next_draws_list and len(next_draws_list) >= 2:
                    # 檢查是否有數字在每一次下一期都出現
                    all_possible_numbers = set()
                    for next_draw in next_draws_list:
                        all_possible_numbers.update(next_draw)
                    
                    numbers_appeared_every_time = []
                    for num in all_possible_numbers:
                        # 計算這個數字在多少期下一期中出現
                        appeared_times = 0
                        for next_draw in next_draws_list:
                            if num in next_draw:
                                appeared_times += 1
                        
                        # 如果在每一期下一期都出現
                        if appeared_times == len(next_draws_list):
                            numbers_appeared_every_time.append(num)
                    
                    if numbers_appeared_every_time:
                        combinations_found.append({
                            'pair': (first_num, second_num),
                            'occurrences': len(indices_with_pair),
                            'must_appear_numbers': sorted(numbers_appeared_every_time),
                            'next_draws_list': next_draws_list
                        })
    
    # 按出現次數排序結果
    combinations_found.sort(key=lambda x: x['occurrences'], reverse=True)
    
    # 輸出結果
    if combinations_found:
        print(f"找到 {len(combinations_found)} 個兩個數字組合的模式:")
        print()
        
        for combo in combinations_found:
            pair = combo['pair']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"數字組合 {pair[0]}, {pair[1]}:")
            print(f" 出現 {occurrences} 次")
            print(f" 下一期每次都出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            for i, next_draw in enumerate(combo['next_draws_list']):
                print(f"   第{i+1}次後的下一期: {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 60)
    else:
        print("沒有找到兩個數字組合出現後，下一期每次都出現特定數字的模式")
    
    # 額外分析：尋找高機率出現的數字
    print("\n" + "="*80)
    print("分析兩個數字組合後，高機率出現的數字 (至少出現80%的時間)...")
    print("="*80)
    
    high_prob_combinations = []
    
    for first_num in range(1, 50):
        for second_num in range(first_num+1, 50):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 檢查出現3次或以上的情況
            if len(indices_with_pair) >= 3:
                # 獲取下一期的所有數字
                next_draws_list = []
                
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draws_list.append(next_draw)
                
                if next_draws_list:
                    # 統計每個數字出現的頻率
                    all_numbers = []
                    for next_draw in next_draws_list:
                        all_numbers.extend(next_draw)
                    
                    number_counts = Counter(all_numbers)
                    
                    # 檢查是否有數字出現頻率達到80%或以上
                    for num, count in number_counts.items():
                        probability = count / len(next_draws_list)
                        if probability >= 0.8:  # 80%或以上機率
                            high_prob_combinations.append({
                                'pair': (first_num, second_num),
                                'number': num,
                                'occurrences': len(indices_with_pair),
                                'appear_count': count,
                                'probability': probability,
                                'total_draws': len(next_draws_list)
                            })
    
    # 按機率排序結果
    high_prob_combinations.sort(key=lambda x: x['probability'], reverse=True)
    
    # 輸出高機率結果
    if high_prob_combinations:
        print(f"找到 {len(high_prob_combinations)} 個高機率模式:")
        print(f"{'數字組合':<12} {'出現數字':<8} {'機率':<8} {'出現次數':<8} {'總次數':<6}")
        print("-" * 60)
        
        for combo in high_prob_combinations[:20]:  # 顯示前20個
            pair = combo['pair']
            probability = combo['probability'] * 100
            print(f"{pair[0]},{pair[1]:<10} {combo['number']:<8} {probability:.1f}%{'':<5} {combo['appear_count']}/{combo['total_draws']:<6}")
    else:
        print("沒有找到出現機率80%以上的模式")

if __name__ == "__main__":
    find_two_number_must_appear_extended()