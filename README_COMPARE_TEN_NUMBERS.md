# 十個數字比對程式說明

## 程式概述

這個程式實現了以下功能：
1. 讀取 `data_compare_lines1.csv` 檔案中的所有六個數字組合
2. 生成用戶指定的十個數字中所有可能的六個數字組合
3. 將這些組合與 CSV 檔案中的數據進行比對
4. 統計並顯示不同匹配數量的組合

## 技術細節

### 組合生成
- 從十個數字中選取六個數字的組合數量為：C(10,6) = 210 組
- 使用 Python 的 `itertools.combinations` 函數生成所有可能的組合

### 比對邏輯
- 對於每一個生成的六個數字組合，程式會與 CSV 檔案中的所有組合進行比對
- 使用集合（set）的交集操作來計算匹配的數字數量
- 記錄每組與 CSV 檔案中最高的匹配數量

### 匹配分類
程式將結果分為以下類別：
- 6個重複號碼：完全匹配 CSV 檔案中的某組數據
- 5個重複號碼：與 CSV 檔案中的某組數據有5個相同數字
- 4個重複號碼：與 CSV 檔案中的某組數據有4個相同數字
- 3個重複號碼：與 CSV 檔案中的某組數據有3個相同數字
- 2個重複號碼：與 CSV 檔案中的某組數據有2個相同數字
- 1個重複號碼：與 CSV 檔案中的某組數據有1個相同數字
- 0個重複號碼：與 CSV 檔案中的所有組合都沒有相同數字

## 程式輸出示例

執行測試程式後的輸出顯示：
- 使用的十個數字：[1, 5, 12, 18, 23, 27, 31, 35, 42, 48]
- 生成的組合總數：210 組
- 5個重複號碼：5 組
- 4個重複號碼：170 組
- 3個重複號碼：35 組
- 2個、1個、0個重複號碼：0 組

## 使用方式

### 互動式版本
運行 `compare_ten_numbers.py` 進行互動式操作：
```bash
python compare_ten_numbers.py
```

### 測試版本
運行 `test_compare_ten_numbers.py` 使用預設數字：
```bash
python test_compare_ten_numbers.py
```

## 檔案說明

- `compare_ten_numbers.py`：主程式，包含互動式輸入功能
- `test_compare_ten_numbers.py`：測試版本，使用預設數字進行測試
- `HOW_TO_USE_NEW.md`：使用說明文件
- `README_COMPARE_TEN_NUMBERS.md`：技術說明文件

## 輸入驗證

程式會驗證用戶輸入：
- 必須恰好十個數字
- 數字不能重複
- 數字範圍必須在1到49之間