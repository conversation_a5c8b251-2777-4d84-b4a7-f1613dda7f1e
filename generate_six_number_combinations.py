import csv
from itertools import combinations

def get_user_input():
    """獲取用戶輸入的十個數字"""
    while True:
        try:
            print("請輸入十個數字（以逗號分隔）：")
            user_input = input().strip()
            numbers = [int(num.strip()) for num in user_input.split(',')]
            
            if len(numbers) != 10:
                print(f"錯誤：您輸入了 {len(numbers)} 個數字，請輸入恰好十個數字。")
                continue
            
            # 檢查數字是否重複
            if len(set(numbers)) != len(numbers):
                print("錯誤：請勿輸入重複的數字。")
                continue
            
            # 檢查數字範圍（假設是1-49）
            if any(num < 1 or num > 49 for num in numbers):
                print("錯誤：數字必須在1到49之間。")
                continue
            
            return numbers
        except ValueError:
            print("錯誤：請輸入有效的數字，以逗號分隔。")

def generate_combinations(numbers, size=6):
    """從十個數字中生成六個數字的所有組合"""
    return list(combinations(numbers, size))

def display_combinations(combinations_list):
    """顯示所有六個數字的組合"""
    print(f"\n從十個數字生成的所有六個數字組合：")
    print("="*40)
    for i, combo in enumerate(combinations_list, 1):
        print(f"{i:3d}. {combo}")
    print("="*40)
    print(f"總共生成了 {len(combinations_list)} 組六個數字的組合")

def main():
    print("六個號碼組合生成器")
    print("此程式將從您輸入的十個數字中生成所有可能的六個數字組合。")
    print()
    
    # 獲取用戶輸入
    user_numbers = get_user_input()
    print(f"\n您輸入的十個數字為: {user_numbers}")
    
    # 生成六個數字的組合
    print("\n正在生成六個數字的所有組合...")
    six_number_combinations = generate_combinations(user_numbers, 6)
    
    # 顯示結果
    display_combinations(six_number_combinations)

if __name__ == "__main__":
    main()