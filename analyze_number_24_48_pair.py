import csv
from collections import Counter

def analyze_number_24_48_pair():
    """
    分析當數字24和48同時出現在同一期時，下一期尾數7的出現情況
    並尋找能與24,48組合一起提高尾數7出現機率的第三個號碼
    """
    # 讀取CSV文件
    with open('data_compare_lines1.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字24和48同時出現的行的索引
    indices_with_24_48 = []
    for i, draw in enumerate(draws):
        if 24 in draw and 48 in draw:
            indices_with_24_48.append(i)
    
    print(f"數字24和48同時在以下期數出現: {[idx+1 for idx in indices_with_24_48]}")
    print(f"總共出現 {len(indices_with_24_48)} 次")
    
    # 檢查這些期數下一期是否出現尾數7
    next_draws_with_digit_7 = 0
    for idx in indices_with_24_48:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_digits = [n % 10 for n in next_draw]
            if 7 in next_draw_digits:
                next_draws_with_digit_7 += 1
            print(f"第 {idx+1} 期({draws[idx]}) -> 第 {idx+2} 期({next_draw}), 包含尾數7: {7 in next_draw_digits}")
    
    if len(indices_with_24_48) > 0:
        percentage = (next_draws_with_digit_7 / len(indices_with_24_48)) * 100
        print(f"\n當24和48同時出現時，下一期出現尾數7的機率: {next_draws_with_digit_7}/{len(indices_with_24_48)} = {percentage:.2f}%")
    
    # 尋找與24,48組合時能提高尾數7出現機率的第三個號碼
    print(f"\n尋找與24,48組合時能提高尾數7出現機率的第三個號碼...")
    
    # 統計與24,48同時出現的其他號碼及其對尾數7的影響
    triple_number_stats = {}
    
    for idx in indices_with_24_48:
        if idx + 1 < len(draws):
            current_draw = draws[idx]  # 當期號碼（包含24和48）
            next_draw = draws[idx + 1]  # 下一期號碼
            
            # 找出當期除了24和48之外的其他號碼
            other_numbers = [num for num in current_draw if num != 24 and num != 48]
            
            # 檢查下一期是否包含尾數7
            next_draw_digits = [n % 10 for n in next_draw]
            has_digit_7 = 7 in next_draw_digits
            
            for num in other_numbers:
                if num not in triple_number_stats:
                    triple_number_stats[num] = {'total': 0, 'with_digit_7': 0}
                
                triple_number_stats[num]['total'] += 1
                if has_digit_7:
                    triple_number_stats[num]['with_digit_7'] += 1
    
    # 過濾掉出現次數少於2次的號碼
    significant_triples = {num: stats for num, stats in triple_number_stats.items() if stats['total'] >= 2}
    
    if significant_triples:
        print(f"\n與24,48同時出現至少2次且對下期尾數7影響的統計:")
        print("(格式: 號碼: [出現次數, 下期有尾數7次數, 成功率])")
        
        sorted_triples = sorted(significant_triples.items(), 
                               key=lambda x: x[1]['with_digit_7']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                               reverse=True)
        
        for num, stats in sorted_triples:
            success_rate = (stats['with_digit_7'] / stats['total']) * 100
            print(f"  數字 {num:2d}: [{stats['total']}, {stats['with_digit_7']}, {success_rate:.2f}%]")
        
        # 顯示成功率最高的幾個號碼
        print(f"\n與24,48組合時，下期產生尾數7機率最高的號碼:")
        top_triples = [(num, stats) for num, stats in sorted_triples if stats['with_digit_7'] > 0]
        for num, stats in top_triples[:10]:  # 顯示前10名
            success_rate = (stats['with_digit_7'] / stats['total']) * 100
            print(f"  數字 {num:2d}: 機率 {success_rate:.2f}% (共 {stats['total']} 次，成功 {stats['with_digit_7']} 次)")
    else:
        print("沒有足夠的數據進行三重號碼分析")
    
    # 分析所有可能的雙號碼組合對尾數7的影響（除了24,48外）
    print(f"\n分析其他號碼與24配對時對尾數7的影響:")
    
    # 找到只有24出現的期數（不含48）
    indices_with_24_only = [i for i in range(len(draws)) if 24 in draws[i] and 48 not in draws[i]]
    
    paired_numbers_stats = {}
    
    for idx in indices_with_24_only:
        if idx + 1 < len(draws):
            current_draw = draws[idx]  # 當期號碼（包含24但不含48）
            next_draw = draws[idx + 1]  # 下一期號碼
            
            # 檢查當期除了24外的其他號碼
            other_numbers = [num for num in current_draw if num != 24]
            
            # 檢查下一期是否包含尾數7
            next_draw_digits = [n % 10 for n in next_draw]
            has_digit_7 = 7 in next_draw_digits
            
            for num in other_numbers:
                pair_key = tuple(sorted([24, num]))  # 使用元組作為鍵，包含24和另一個號碼
                if pair_key not in paired_numbers_stats:
                    paired_numbers_stats[pair_key] = {'total': 0, 'with_digit_7': 0}
                
                paired_numbers_stats[pair_key]['total'] += 1
                if has_digit_7:
                    paired_numbers_stats[pair_key]['with_digit_7'] += 1
    
    # 過濾掉出現次數少於3次的配對
    significant_pairs = {pair: stats for pair, stats in paired_numbers_stats.items() if stats['total'] >= 3}
    
    if significant_pairs:
        print(f"\n與24配對至少3次且對下期尾數7影響的統計:")
        print("(格式: 配對號碼: [出現次數, 下期有尾數7次數, 成功率])")
        
        sorted_pairs = sorted(significant_pairs.items(), 
                              key=lambda x: x[1]['with_digit_7']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                              reverse=True)
        
        for pair, stats in sorted_pairs[:15]:  # 顯示前15名
            success_rate = (stats['with_digit_7'] / stats['total']) * 100
            print(f"  數字 {pair[0]:2d},{pair[1]:2d}: [{stats['total']}, {stats['with_digit_7']}, {success_rate:.2f}%]")
    
    # 分析數字48與其他號碼的配對對尾數7的影響（排除同時有24的情況）
    print(f"\n分析其他號碼與48配對時對尾數7的影響（排除同時有24的情況）:")
    
    # 找到只有48出現的期數（不含24）
    indices_with_48_only = [i for i in range(len(draws)) if 48 in draws[i] and 24 not in draws[i]]
    
    paired_numbers_48_stats = {}
    
    for idx in indices_with_48_only:
        if idx + 1 < len(draws):
            current_draw = draws[idx]  # 當期號碼（包含48但不含24）
            next_draw = draws[idx + 1]  # 下一期號碼
            
            # 檢查當期除了48外的其他號碼
            other_numbers = [num for num in current_draw if num != 48]
            
            # 檢查下一期是否包含尾數7
            next_draw_digits = [n % 10 for n in next_draw]
            has_digit_7 = 7 in next_draw_digits
            
            for num in other_numbers:
                pair_key = tuple(sorted([48, num]))  # 使用元組作為鍵，包含48和另一個號碼
                if pair_key not in paired_numbers_48_stats:
                    paired_numbers_48_stats[pair_key] = {'total': 0, 'with_digit_7': 0}
                
                paired_numbers_48_stats[pair_key]['total'] += 1
                if has_digit_7:
                    paired_numbers_48_stats[pair_key]['with_digit_7'] += 1
    
    # 過濾掉出現次數少於3次的配對
    significant_pairs_48 = {pair: stats for pair, stats in paired_numbers_48_stats.items() if stats['total'] >= 3}
    
    if significant_pairs_48:
        print(f"\n與48配對至少3次且對下期尾數7影響的統計（不含24）:")
        print("(格式: 配對號碼: [出現次數, 下期有尾數7次數, 成功率])")
        
        sorted_pairs_48 = sorted(significant_pairs_48.items(), 
                                key=lambda x: x[1]['with_digit_7']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                reverse=True)
        
        for pair, stats in sorted_pairs_48[:15]:  # 顯示前15名
            success_rate = (stats['with_digit_7'] / stats['total']) * 100
            print(f"  數字 {pair[0]:2d},{pair[1]:2d}: [{stats['total']}, {stats['with_digit_7']}, {success_rate:.2f}%]")

if __name__ == "__main__":
    analyze_number_24_48_pair()