# 快速開始指南

## 安裝步驟

1. 確保您的系統已安裝 Python 3.7 或更高版本
2. 安裝依賴套件：

```bash
pip install -r requirements.txt
```

## 基本使用方法

### 方法一：互動模式
```bash
python -m lottery_analysis_rebuild.main
```

### 方法二：命令列參數
```bash
python -m lottery_analysis_rebuild.main "1,2,3,4,5,6,7,8"
```

### 方法三：使用執行腳本
```bash
python run_analysis.py --numbers "1,2,3,4,5,6,7,8"
```

## 進階功能

### 1. 進階分析
```bash
python run_analysis.py --numbers "1,2,3,4,5,6,7,8" --advanced
```

### 2. 視覺化圖表
```bash
python run_analysis.py --numbers "1,2,3,3,5,6,7,8" --advanced --visualize
```

### 3. 互動模式
```bash
python run_analysis.py --interactive
```

### 4. 指定CSV檔案
```bash
python run_analysis.py --file "../data_compare_lines2.csv" --numbers "1,2,3,4,5,6,7,8"
```

## 程式碼範例

### 基本比較
```python
from lottery_analysis_rebuild import LotteryAnalyzer

# 建立分析器
analyzer = LotteryAnalyzer('../data_compare_lines1.csv')

# 比較號碼
test_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
results = analyzer.compare_numbers(test_numbers)

# 顯示結果
for count, matches in results.items():
    print(f"{count} 個重複號碼: {len(matches)} 筆")
```

### 進階分析
```python
from lottery_analysis_rebuild import AdvancedLotteryAnalyzer

# 建立進階分析器
analyzer = AdvancedLotteryAnalyzer('../data_compare_lines1.csv')

# 生成詳細報告
report = analyzer.generate_detailed_report()

# 創建視覺化圖表
analyzer.create_visualization('my_analysis.png')
```

## 使用範例腳本

執行範例腳本了解各種功能：

```bash
python example_usage.py
```

## 檔案說明

- `config.py` - 配置設定
- `data_loader.py` - 資料載入功能
- `analyzer.py` - 基本分析功能
- `advanced_analyzer.py` - 進階分析功能
- `main.py` - 主程式入口
- `run_analysis.py` - 功能完整的執行腳本
- `example_usage.py` - 使用範例
- `requirements.txt` - 依賴套件

## 注意事項

1. CSV檔案應包含以逗號分隔的數字
2. 預設比較8個號碼，最少3個重複才會顯示
3. 號碼範圍預設為1-49
4. 確保CSV檔案編碼為UTF-8