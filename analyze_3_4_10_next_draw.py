import csv
from collections import Counter
import os

def analyze_3_4_10_next_draw():
    """
    分析數字3、4、10一起出現後，下一期各尾數的出現分布
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找出所有包含3、4、10的抽獎
    indices_with_3_4_10 = []
    for i, draw in enumerate(draws):
        if 3 in draw and 4 in draw and 10 in draw:
            indices_with_3_4_10.append(i)
    
    print(f"數字 3、4、10 一起出現的總次數: {len(indices_with_3_4_10)}")
    
    if len(indices_with_3_4_10) == 0:
        print("在數據中沒有找到數字3、4、10同時出現的情況")
        return
    
    # 獲取3、4、10出現後的下一期抽獎數字
    next_draw_numbers = []
    for idx in indices_with_3_4_10:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
    
    if not next_draw_numbers:
        print("沒有下一期的數據可用")
        return
    
    print(f"數字 3、4、10 一起出現後，下一期共有 {len(next_draw_numbers)} 個數字")
    
    # 分析尾數分布
    last_digits = [num % 10 for num in next_draw_numbers]
    last_digit_counts = Counter(last_digits)
    
    print(f"\n下一期數字的尾數分布:")
    print("-" * 30)
    for digit in sorted(last_digit_counts.keys()):
        count = last_digit_counts[digit]
        percentage = (count / len(last_digits)) * 10
        print(f"  尾數 {digit}: {count} 次 ({percentage:.2f}%)")
    
    # 找出出現次數最多的尾數
    most_common_digit = last_digit_counts.most_common(1)[0]
    print(f"\n出現次數最多的尾數: {most_common_digit[0]} (共 {most_common_digit[1]} 次，{most_common_digit[1]/len(last_digits)*100:.2f}%)")
    
    # 顯示所有尾數按出現次數排序
    print(f"\n所有尾數按出現次數排序:")
    print("-" * 30)
    all_digits_sorted = last_digit_counts.most_common()
    for i, (digit, count) in enumerate(all_digits_sorted, 1):
        percentage = (count / len(last_digits)) * 10
        print(f"  {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")

if __name__ == "__main__":
    analyze_3_4_10_next_draw()