import csv
from collections import Counter
import os

def analyze_5_8_with_third_number_for_digit_8():
    """
    分析在數字5、8基礎上再添加一個數字，對尾數8機率的影響
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print(f"分析數字5、8及第三個數字對下一期尾數8機率的影響")
    print("="*60)
    
    # 存儲結果
    results = []
    
    # 嘗試添加1到49的每個數字來看對尾數8機率的影響
    for third_number in range(1, 50):
        if third_number == 5 or third_number == 8:
            continue  # 跳過5和8，因為它們已經在組合中
        
        # 找出包含5、8和第三個數字的抽獎
        indices_with_5_8_third = []
        for i, draw in enumerate(draws):
            if 5 in draw and 8 in draw and third_number in draw:
                indices_with_5_8_third.append(i)
        
        if len(indices_with_5_8_third) >= 2:  # 只分析至少有2次出現的情況
            # 獲取5、8和第三個數字一起出現後的下一期抽獎數字
            next_draw_numbers = []
            for idx in indices_with_5_8_third:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers.extend(next_draw)
            
            if next_draw_numbers:
                last_digits = [num % 10 for num in next_draw_numbers]
                last_digit_counts = Counter(last_digits)
                
                # 計算尾數8的出現次數和機率
                digit_8_count = last_digit_counts.get(8, 0)
                digit_8_percentage = (digit_8_count / len(last_digits)) * 10 if len(last_digits) > 0 else 0
                
                results.append({
                    'third_number': third_number,
                    'occurrences': len(indices_with_5_8_third),
                    'digit_8_count': digit_8_count,
                    'digit_8_percentage': digit_8_percentage,
                    'total_numbers': len(last_digits)
                })
    
    # 按尾數8機率排序結果
    results.sort(key=lambda x: x['digit_8_percentage'], reverse=True)
    
    print(f"基礎情況 (僅5,8): 尾數8機率為 1.18% (24次)")
    print()
    print(f"添加第三個數字後尾數8機率排名 (至少2次出現):")
    print("-" * 60)
    print(f"{'排名':<4} {'第三個數字':<6} {'出現次數':<6} {'尾數8次數':<6} {'尾數8機率':<10} {'總數字數':<6}")
    print("-" * 60)
    
    for i, result in enumerate(results[:20], 1):  # 顯示前20個最佳結果
        print(f"{i:<4} {result['third_number']:<6} {result['occurrences']:<6} {result['digit_8_count']:<6} {result['digit_8_percentage']:<10.2f}% {result['total_numbers']:<6}")
    
    # 特別標出顯著提高尾數8機率的組合
    print(f"\n顯著提高尾數8機率的組合 (機率 > 2%):")
    print("-" * 40)
    significant_improvements = [r for r in results if r['digit_8_percentage'] > 2.0]
    
    if significant_improvements:
        for result in significant_improvements:
            print(f" 數字 5, 8, {result['third_number']}: {result['digit_8_percentage']:.2f}% (尾數8出現 {result['digit_8_count']} 次, 共 {result['total_numbers']} 個數字) - 出現 {result['occurrences']} 次")
    else:
        print("  沒有找到尾數8機率超過2%的組合")
    
    # 找出在至少3次出現的情況下尾數8機率最高的組合
    print(f"\n至少3次出現且尾數8機率最高的組合:")
    print("-" * 40)
    high_occurrence_results = [r for r in results if r['occurrences'] >= 3]
    if high_occurrence_results:
        high_occurrence_results.sort(key=lambda x: x['digit_8_percentage'], reverse=True)
        for result in high_occurrence_results[:10]:
            print(f" 數字 5, 8, {result['third_number']}: {result['digit_8_percentage']:.2f}% (尾數8出現 {result['digit_8_count']} 次, 共 {result['total_numbers']} 個數字) - 出現 {result['occurrences']} 次")
    else:
        print("  沒有找到至少3次出現的組合")

if __name__ == "__main__":
    analyze_5_8_with_third_number_for_digit_8()