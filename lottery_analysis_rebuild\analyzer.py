"""彩券號碼分析器"""

import sys
from itertools import combinations
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from config import Config
from data_loader import load_csv_data


@dataclass
class MatchResult:
    """匹配結果資料類別"""
    line: int
    numbers: List[int]
    count: int
    original_row: List[int]


class LotteryAnalyzer:
    """彩券分析器主類別"""
    
    def __init__(self, csv_file: str = Config.DEFAULT_CSV_FILE):
        """
        初始化分析器
        
        Args:
            csv_file: CSV檔案路徑
        """
        self.csv_file = csv_file
        self.data = self._load_csv_data()
    
    def _load_csv_data(self) -> List[Set[int]]:
        """載入CSV資料"""
        return load_csv_data(self.csv_file)
    
    def compare_numbers(self, input_numbers: List[int], 
                       min_match_count: int = Config.DEFAULT_MIN_MATCH_COUNT) -> Dict[int, List[MatchResult]]:
        """
        比較號碼並返回結果
        
        Args:
            input_numbers: 輸入的號碼列表
            min_match_count: 最小匹配數量
        
        Returns:
            包含各匹配數量結果的字典
        """
        from data_loader import validate_input_numbers
        validate_input_numbers(input_numbers)
        
        input_set = set(input_numbers)
        results = {i: [] for i in range(min_match_count, 8)}  # 只記錄min_match_count到7個匹配
        
        for line_idx, row_set in enumerate(self.data, start=1):
            common_numbers = input_set.intersection(row_set)
            common_count = len(common_numbers)
            
            if min_match_count <= common_count <= 7:
                results[common_count].append(MatchResult(
                    line=line_idx,
                    numbers=sorted(list(common_numbers)),
                    count=common_count,
                    original_row=sorted(list(row_set))
                ))
        
        return results
    
    def find_exact_match(self, input_numbers: List[int]) -> int:
        """
        尋找完全匹配的行號
        
        Args:
            input_numbers: 要查找的號碼列表
            
        Returns:
            匹配的行號，如果沒有匹配則返回None
        """
        input_set = set(input_numbers)
        for line_idx, row_set in enumerate(self.data, start=1):
            if input_set == row_set:
                return line_idx
        return None
    
    def get_number_statistics(self, input_numbers: List[int]) -> Dict[int, int]:
        """
        獲取輸入號碼在CSV中的出現統計
        
        Args:
            input_numbers: 輸入的號碼列表
            
        Returns:
            每個號碼的出現次數字典
        """
        number_counts = {}
        for num in input_numbers:
            count = sum(1 for row_set in self.data if num in row_set)
            number_counts[num] = count
        return number_counts
    
    def generate_combinations(self, numbers: List[int], size: int = Config.COMBINATION_SIZE) -> List[Tuple[int, ...]]:
        """
        從給定號碼中生成指定大小的所有組合
        
        Args:
            numbers: 原始號碼列表
            size: 組合大小
            
        Returns:
            所有可能的組合列表
        """
        return list(combinations(numbers, size))