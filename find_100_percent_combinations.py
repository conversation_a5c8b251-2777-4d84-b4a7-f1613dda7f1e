import csv
from collections import Counter
import os

def find_100_percent_combinations():
    """
    尋找在特定數字組合出現後，下一期有100%機率出現特定尾數的組合
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("尋找100%出現特定尾數的數字組合...")
    print("="*60)
    
    # 測試不同的數字組合
    for first_num in range(1, 50):
        for second_num in range(first_num+1, 50):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 只考慮至少出現3次的組合以確保統計意義
            if len(indices_with_pair) >= 3:
                # 獲取下一期的數字
                next_draw_numbers = []
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draw_numbers.extend(next_draw)
                
                if next_draw_numbers:
                    # 分析尾數
                    last_digits = [num % 10 for num in next_draw_numbers]
                    last_digit_counts = Counter(last_digits)
                    
                    # 檢查是否有尾數達到100%出現率
                    for digit in range(0, 10):
                        if last_digit_counts.get(digit, 0) == len(last_digits) and len(last_digits) > 0:
                            # 這表示所有數字的尾數都是digit
                            print(f"發現100%尾數組合: 數字 {first_num}, {second_num} → 尾數 {digit}")
                            print(f"  出現 {len(indices_with_pair)} 次，下一期共 {len(last_digits)} 個數字")
                            print(f"  所有數字的尾數都是 {digit}")
                            
                            # 顯示具體的數字
                            numbers_with_target_digit = [num for num in next_draw_numbers if num % 10 == digit]
                            print(f"  具體數字: {sorted(set(numbers_with_target_digit))}")
                            print("-" * 40)
    
    print("\n尋找包含三個數字的組合...")
    print("="*60)
    
    # 測試三個數字的組合
    for first_num in range(1, 30):  # 限制範圍以加快計算
        for second_num in range(first_num+1, 35):
            for third_num in range(second_num+1, 40):
                # 找出同時包含三個數字的抽獎
                indices_with_triplet = []
                for i, draw in enumerate(draws):
                    if first_num in draw and second_num in draw and third_num in draw:
                        indices_with_triplet.append(i)
                
                # 只考慮至少出現2次的組合
                if len(indices_with_triplet) >= 2:
                    # 獲取下一期的數字
                    next_draw_numbers = []
                    for idx in indices_with_triplet:
                        if idx + 1 < len(draws):
                            next_draw = draws[idx + 1]
                            next_draw_numbers.extend(next_draw)
                    
                    if next_draw_numbers:
                        # 分析尾數
                        last_digits = [num % 10 for num in next_draw_numbers]
                        last_digit_counts = Counter(last_digits)
                        
                        # 檢查是否有尾數達到100%出現率
                        for digit in range(0, 10):
                            if last_digit_counts.get(digit, 0) == len(last_digits) and len(last_digits) > 0:
                                # 這表示所有數字的尾數都是digit
                                print(f"發現100%尾數組合: 數字 {first_num}, {second_num}, {third_num} → 尾數 {digit}")
                                print(f"  出現 {len(indices_with_triplet)} 次，下一期共 {len(last_digits)} 個數字")
                                print(f"  所有數字的尾數都是 {digit}")
                                
                                # 顯示具體的數字
                                numbers_with_target_digit = [num for num in next_draw_numbers if num % 10 == digit]
                                print(f"  具體數字: {sorted(set(numbers_with_target_digit))}")
                                print("-" * 40)

if __name__ == "__main__":
    find_100_percent_combinations()