import csv
from itertools import combinations

def load_csv_data(filename):
    """Load all data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer list
            numbers = [int(num.strip()) for num in row]
            data.append(numbers)
    return data

def find_combinations_from_line(csv_data, line_index):
    """Find all 5-number combinations from a specific line that exist in other lines"""
    target_line = csv_data[line_index]
    target_set = set(target_line)
    
    print(f"Analyzing line {line_index + 1}: {sorted(target_line)}")
    print()
    
    # Generate all 5-number combinations from this line
    all_combinations = list(combinations(target_line, 5))
    print(f"All possible 5-number combinations from this line ({len(all_combinations)} total):")
    for i, combo in enumerate(all_combinations):
        print(f"  {i+1}: {sorted(combo)}")
    print()
    
    # Check which combinations appear in other lines
    found_combinations = []
    for combo in all_combinations:
        combo_set = set(combo)
        found_in_lines = []
        
        for idx, line in enumerate(csv_data):
            if idx != line_index:  # Don't compare with itself
                line_set = set(line)
                if combo_set.issubset(line_set):
                    found_in_lines.append((idx + 1, sorted(line)))
        
        if found_in_lines:
            found_combinations.append({
                'combination': sorted(combo),
                'found_in': found_in_lines
            })
            print(f"O Combination {sorted(combo)} found in {len(found_in_lines)} other line(s):")
            for line_num, line_content in found_in_lines:
                print(f"    Line {line_num}: {line_content}")
        else:
            print(f"X Combination {sorted(combo)} NOT found in any other line")
    
    print()
    print(f"Summary: {len(found_combinations)}/{len(all_combinations)} combinations were found in other lines")
    
    return found_combinations

def main():
    print("Checking all 5-number combinations from a specific line against all other lines...")
    print("=" * 80)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Check line 2059 (index 2058) which is [13, 16, 18, 22, 39, 42]
    print()
    find_combinations_from_line(csv_data, 2058)  # 0-indexed, so line 2059 is index 2058
    
    # Also check line 1760 (index 1759) which is [13, 16, 18, 22, 34, 42]
    print("=" * 80)
    print("Checking the other line (1760) as well:")
    find_combinations_from_line(csv_data, 1759)  # 0-indexed, so line 1760 is index 1759)

if __name__ == "__main__":
    main()