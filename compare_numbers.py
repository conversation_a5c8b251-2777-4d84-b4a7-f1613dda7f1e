import csv
import sys
from itertools import combinations

def load_csv_data(filename):
    """Load number data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer set
            numbers = {int(num.strip()) for num in row}
            data.append(numbers)
    return data

def compare_numbers_with_combinations(input_numbers, csv_data):
    """Compare input numbers with CSV numbers and generate all possible combinations"""
    input_set = set(input_numbers)
    all_combination_matches = {}
    
    # Initialize dictionary to store all combinations by count
    for count in range(1, 8):  # 1 to 7 duplicates
        all_combination_matches[count] = []
    
    for i, row_set in enumerate(csv_data, start=1):
        # Calculate intersection to find common numbers
        common_numbers = input_set.intersection(row_set)
        if common_numbers:
            common_list = sorted(list(common_numbers))
            row_original = sorted(list(row_set))
            
            # Generate all possible combinations for this row
            for count in range(1, len(common_list) + 1):
                if count >= 3:  # Only include 3 or more duplicates (exclude 1 and 2)
                    # Generate all combinations of 'count' numbers from common_numbers
                    for combo in combinations(common_list, count):
                        all_combination_matches[count].append({
                            'line': i,
                            'numbers': list(combo),
                            'count': count,
                            'original_row': row_original
                        })
    
    return all_combination_matches

def main():
    print("Number comparison program")
    print("Comparing with 'data_compare_lines1.csv' file")
    print("-" * 50)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Get input numbers from command line arguments, or interactively, or use default
    if len(sys.argv) > 1:
        try:
            input_numbers = [int(num.strip()) for num in sys.argv[1].split(',')]
        except ValueError:
            print("Please provide valid numbers separated by commas as argument")
            return
    else:
        # Interactive input
        while True:
            try:
                input_str = input("請輸入八個號碼 (以逗號分隔，例如: 1,2,3,4,5,6,7,8): ")
                input_numbers = [int(num.strip()) for num in input_str.split(',')]

                if len(input_numbers) != 8:
                    print("請輸入剛好八個號碼!")
                    continue

                if len(set(input_numbers)) != 8:
                    print("請輸入八個不重複的號碼!")
                    continue
                    
                break
            except ValueError:
                print("請輸入有效的數字，以逗號分隔!")
    
    print(f"輸入號碼: {sorted(input_numbers)}")
    print("-" * 50)
    
    # Compare numbers with combinations
    all_combination_matches = compare_numbers_with_combinations(input_numbers, csv_data)
    
    # Check for exact match
    exact_match_line = None
    for i, row_set in enumerate(csv_data, start=1):
        if set(input_numbers) == row_set:
            exact_match_line = i
            break
    
    if exact_match_line:
        print(f"發現完全一樣的號碼組合! 位於第 {exact_match_line} 行")
        print(f"該行號碼: {sorted(list(csv_data[exact_match_line-1]))}")
        print()
    else:
        print("沒有找到完全一樣的號碼組合")
        print()
    
    # Display partial matches (3 or more duplicates)
    total_partial_matches = sum(len(matches) for count, matches in all_combination_matches.items() if count >= 3 and count < 8)
    if total_partial_matches > 0:
        print(f"找到 {total_partial_matches} 筆部分匹配的資料（包含所有可能的組合）:")
        
        # Display matches by count (excluding 1 and 2)
        for count in sorted(range(3, 8), reverse=True):  # 7 to 3
            count_matches = all_combination_matches[count]
            if count_matches:
                print(f"\n有 {len(count_matches)} 筆資料包含 {count} 個重複號碼:")
                for match in count_matches[:10]:  # Show first 10 matches for each count
                    print(f"  第 {match['line']} 行: 重複號碼 {match['numbers']} / 該行號碼: {match['original_row']}")
                if len(count_matches) > 10:
                    print(f"  ... 還有 {len(count_matches) - 10} 筆資料")
        print()
    else:
        print("沒有找到任何部分匹配的資料（除了1個或2個重複號碼）")
    
    # Show statistics
    print("-" * 50)
    print("統計資訊:")
    
    # Count occurrences of each input number in CSV
    number_counts = {}
    for num in input_numbers:
        count = 0
        for row_set in csv_data:
            if num in row_set:
                count += 1
        number_counts[num] = count
    
    print("輸入號碼在CSV中的出現次數:")
    for num in sorted(input_numbers):
        print(f"  號碼 {num:2d}: {number_counts[num]} 次")
    
    # Count matches by duplicate number count (excluding 1 and 2)
    print("\n各重複數量的匹配筆數:")
    for count in sorted(range(3, 8), reverse=True):  # 7 to 3
        matches_count = len(all_combination_matches[count])
        if matches_count > 0:
            print(f" {count} 個重複號碼: {matches_count} 筆")

if __name__ == "__main__":
    main()
