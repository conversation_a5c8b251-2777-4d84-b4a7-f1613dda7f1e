# 1到49號碼反向追蹤分析專案總結

## 專案概述
本專案實現了一個完整的反向追蹤分析系統，用於分析彩票中1到49號碼的出現模式。系統會分析每個號碼在歷史數據中出現後，前7期內最常出現的號碼，並將結果記錄在字典中。

## 檔案結構
- `reverse_tracking_analysis.py` - 主要分析邏輯
- `README.md` - 專案說明文件
- `results.json` - 分析結果文件

## 核心功能
1. 讀取CSV格式的彩票開獎數據
2. 分析1到49每個號碼在歷史中出現後，前2期內最常同時出現三個的號碼組合
3. 將分析結果記錄在字典中，包含號碼組合、出現次數和百分比
4. 保存結果到JSON文件供後續使用
5. 提供加載和查詢功能

## 資料結構
分析結果存儲在字典中，格式如下：
```python
{
    "1": [
        {
            "number": 8,
            "count": 274,
            "percentage": 2.4
        },
        ...
    ],
    "2": [
        {
            "number": 8,
            "count": 279,
            "percentage": 2.4
        },
        ...
    ],
    ...
}
```

## 分析結果
- 系統成功分析了所有49個號碼的反向追蹤模式
- 每個號碼記錄前5名在前7期內最常出現的伴隨號碼
- 計算每個伴隨號碼的出現次數和百分比

## 使用方法
1. 確保有包含彩票開獎數據的CSV文件
2. 執行 `python reverse_tracking_analysis.py`
3. 查看終端機輸出或 `results.json` 文件

## 特色功能
- 可自定義回顧期數（預設為5期）
- 提供最後一期開獎號碼的專屬分析
- 結果以JSON格式保存，方便後續處理
- 包含完整的錯誤處理機制