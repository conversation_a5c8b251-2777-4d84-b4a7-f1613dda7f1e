"""安裝設定檔 - 讓CLI工具可以作為命令列指令使用"""

from setuptools import setup, find_packages

setup(
    name="lottery-analysis-cli",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
    ],
    entry_points={
        'console_scripts': [
            'lottery-analyzer=lottery_analysis_rebuild.cli_tool:main',
        ],
    },
    author="Lottery Analysis Tool",
    description="彩券號碼分析CLI工具",
    long_description=open("README.md", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/lottery-analysis",
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.7',
)