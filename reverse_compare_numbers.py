import csv
import sys
from itertools import combinations

def load_csv_data(filename):
    """Load all data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer list
            numbers = [int(num.strip()) for num in row]
            data.append(numbers)
    return data

def compare_with_line(input_numbers, line_numbers):
    """Compare two number lists, return common numbers"""
    input_set = set(input_numbers)
    line_set = set(line_numbers)
    common_numbers = input_set.intersection(line_set)
    return sorted(list(common_numbers))

def find_matches_from_end_to_beginning(csv_data):
    """Compare from the last entry backwards to the beginning, each with previous lines"""
    print("Comparing from the last entry backwards to the beginning...")
    print("-" * 60)
    
    total_6_matches = 0
    total_5_matches = 0
    total_4_matches = 0
    
    # Process each line starting from the end, going to the beginning
    for i in range(len(csv_data) - 1, -1, -1):
        current_line = csv_data[i]
        current_line_number = i + 1
        
        # Compare with lines that come before it (with smaller index)
        matches_6 = []
        matches_5 = []
        matches_4 = []
        
        for j in range(i - 1, -1, -1):  # Compare with previous lines
            compare_line = csv_data[j]
            common_numbers = compare_with_line(current_line, compare_line)
            
            if len(common_numbers) == 6:
                matches_6.append({
                    'line_number': j + 1,
                    'common_numbers': common_numbers,
                    'original_line': sorted(compare_line)
                })
            elif len(common_numbers) == 5:
                matches_5.append({
                    'line_number': j + 1,
                    'common_numbers': common_numbers,
                    'original_line': sorted(compare_line)
                })
            elif len(common_numbers) == 4:
                matches_4.append({
                    'line_number': j + 1,
                    'common_numbers': common_numbers,
                    'original_line': sorted(compare_line)
                })
        
        # Print matches for this line (only 5 duplicate numbers)
        for match in matches_5:
            print(f"Line {current_line_number}: {sorted(current_line)} 對應行 {match['line_number']}: {match['original_line']} 含有 5 個重複的數字 {match['common_numbers']}")
        
        total_6_matches += len(matches_6)
        total_5_matches += len(matches_5)
        total_4_matches += len(matches_4)
    
    return total_6_matches, total_5_matches, total_4_matches

def main():
    print("Compare from the last entry backwards to the beginning")
    print("=" * 60)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Compare from the last entry backwards to the beginning
    total_6_matches, total_5_matches, total_4_matches = find_matches_from_end_to_beginning(csv_data)
    
    print("\n" + "=" * 60)
    print("Comparison Results Summary:")
    print(f"Total 6 duplicate numbers matches: {total_6_matches}")
    print(f"Total 5 duplicate numbers matches: {total_5_matches}")
    print(f"Total 4 duplicate numbers matches: {total_4_matches}")
    
    print("\nProgram completed!")

if __name__ == "__main__":
    main()