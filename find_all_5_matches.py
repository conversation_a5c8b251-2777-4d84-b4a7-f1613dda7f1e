import csv
from itertools import combinations

def load_csv_data(filename):
    """Load all data from CSV file"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            # Convert each row of numbers to integer list
            numbers = [int(num.strip()) for num in row]
            data.append(numbers)
    return data

def find_all_5_number_matches(csv_data):
    """Find all pairs of rows that have exactly 5 common numbers"""
    matches = []
    
    for i in range(len(csv_data)):
        for j in range(i + 1, len(csv_data)):
            row1 = set(csv_data[i])
            row2 = set(csv_data[j])
            common_numbers = row1.intersection(row2)
            
            if len(common_numbers) == 5:
                matches.append({
                    'line1': i + 1,
                    'line2': j + 1,
                    'row1': sorted(csv_data[i]),
                    'row2': sorted(csv_data[j]),
                    'common_numbers': sorted(list(common_numbers))
                })
    
    return matches

def main():
    print("Finding all pairs of rows with exactly 5 common numbers...")
    print("=" * 60)
    
    # Load CSV data
    try:
        csv_data = load_csv_data('data_compare_lines1.csv')
        print(f"Successfully loaded {len(csv_data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Find all matches with 5 common numbers
    matches = find_all_5_number_matches(csv_data)
    
    print(f"Found {len(matches)} pairs of rows with exactly 5 common numbers")
    print()
    
    # Show all matches
    for match in matches:
        print(f"Line {match['line1']}: {match['row1']} and Line {match['line2']}: {match['row2']} have 5 common numbers: {match['common_numbers']}")
    
    print("\n" + "=" * 60)
    print("Summary:")
    print(f"Total pairs with 5 common numbers: {len(matches)}")

if __name__ == "__main__":
    main()