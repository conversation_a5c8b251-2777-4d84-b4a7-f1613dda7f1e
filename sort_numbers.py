def sort_numbers(numbers):
    """
    排序號碼列表，移除重複並按升序排列

    Args:
        numbers (list): 輸入的號碼列表

    Returns:
        list: 排序後的唯一號碼列表
    """
    # 移除重複並排序
    unique_sorted = sorted(list(set(numbers)))
    return unique_sorted

def main():
    # 測試範例
    test_numbers = [2, 4, 6]
    print(f"原始號碼: {test_numbers}")
    sorted_numbers = sort_numbers(test_numbers)
    print(f"排序後: {sorted_numbers}")

    # 測試包含重複的號碼
    duplicate_numbers = [6, 2, 4, 2, 6, 8]
    print(f"\n包含重複的號碼: {duplicate_numbers}")
    sorted_unique = sort_numbers(duplicate_numbers)
    print(f"排序並移除重複後: {sorted_unique}")

if __name__ == "__main__":
    main()
