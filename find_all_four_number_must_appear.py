import csv
from collections import Counter
import os
from itertools import combinations

def find_all_four_number_must_appear():
    """
    尋找所有四個數字組合出現後，下一期一定會出現的數字
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("全面分析1-49範圍內所有四個數字組合...")
    print("尋找下一期一定會出現的數字 (至少出現2次才有統計意義)")
    print("="*80)
    
    # 找出在數據中實際出現的四個數字組合
    actual_quadruplets = Counter()
    
    for draw in draws:
        if len(draw) >= 4:  # 確保抽獎包含至少4個數字
            for quadruplet in combinations(sorted(draw), 4):
                actual_quadruplets[quadruplet] += 1
    
    print(f"在數據中總共找到 {len(actual_quadruplets)} 個四個數字組合")
    
    # 找出現2次或以上的組合
    frequent_quadruplets = [quadruplet for quadruplet, count in actual_quadruplets.items() if count >= 2]
    print(f"其中出現2次或以上的組合有 {len(frequent_quadruplets)} 個")
    
    if len(frequent_quadruplets) == 0:
        print("沒有找到出現2次或以上的四個數字組合")
        return
    
    # 檢查這些組合後是否一定出現特定數字
    must_appear_combinations = []
    
    for quadruplet in frequent_quadruplets:
        first_num, second_num, third_num, fourth_num = quadruplet
        
        # 找出包含這個組合的所有抽獎
        indices_with_quadruplet = []
        for i, draw in enumerate(draws):
            if first_num in draw and second_num in draw and third_num in draw and fourth_num in draw:
                indices_with_quadruplet.append(i)
        
        # 獲取下一期的數據
        next_draws_list = []
        for idx in indices_with_quadruplet:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_draws_list.append(next_draw)
        
        if next_draws_list and len(next_draws_list) >= 2:
            # 檢查是否有數字在每一次下一期都出現
            all_possible_numbers = set()
            for next_draw in next_draws_list:
                all_possible_numbers.update(next_draw)
            
            numbers_appeared_every_time = []
            for num in all_possible_numbers:
                # 計算這個數字在多少期下一期中出現
                appeared_times = 0
                for next_draw in next_draws_list:
                    if num in next_draw:
                        appeared_times += 1
                
                # 如果在每一期下一期都出現
                if appeared_times == len(next_draws_list):
                    numbers_appeared_every_time.append(num)
            
            if numbers_appeared_every_time:
                must_appear_combinations.append({
                    'quadruplet': quadruplet,
                    'occurrences': len(indices_with_quadruplet),
                    'must_appear_numbers': sorted(numbers_appeared_every_time),
                    'next_draws_list': next_draws_list
                })
    
    # 輸出結果
    if must_appear_combinations:
        print(f"\n找到 {len(must_appear_combinations)} 個四個數字組合的模式:")
        print("="*80)
        
        # 按數字組合的字典順序排序（從小到大）
        must_appear_combinations.sort(key=lambda x: x['quadruplet'])
        
        for i, combo in enumerate(must_appear_combinations, 1):
            quadruplet = combo['quadruplet']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"{i}. 數字組合 {quadruplet[0]}, {quadruplet[1]}, {quadruplet[2]}, {quadruplet[3]}:")
            print(f"   出現 {occurrences} 次")
            print(f"   下一期每次都出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            for j, next_draw in enumerate(combo['next_draws_list']):
                print(f"     第{j+1}次後的下一期: {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 60)
    else:
        print(f"\n在 {len(frequent_quadruplets)} 個出現2次或以上的四個數字組合中，")
        print("沒有找到下一期一定出現特定數字的模式")
    
    # 也檢查出現3次或以上的情況
    print("\n" + "="*80)
    print("擴大範圍：檢查出現3次或以上的組合...")
    
    frequent_quadruplets_3plus = [quadruplet for quadruplet, count in actual_quadruplets.items() if count >= 3]
    print(f"出現3次或以上的組合有 {len(frequent_quadruplets_3plus)} 個")
    
    must_appear_combinations_3plus = []
    
    for quadruplet in frequent_quadruplets_3plus:
        first_num, second_num, third_num, fourth_num = quadruplet
        
        # 找出包含這個組合的所有抽獎
        indices_with_quadruplet = []
        for i, draw in enumerate(draws):
            if first_num in draw and second_num in draw and third_num in draw and fourth_num in draw:
                indices_with_quadruplet.append(i)
        
        # 獲取下一期的數據
        next_draws_list = []
        for idx in indices_with_quadruplet:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_draws_list.append(next_draw)
        
        if next_draws_list and len(next_draws_list) >= 3:
            # 檢查是否有數字在每一次下一期都出現
            all_possible_numbers = set()
            for next_draw in next_draws_list:
                all_possible_numbers.update(next_draw)
            
            numbers_appeared_every_time = []
            for num in all_possible_numbers:
                # 計算這個數字在多少期下一期中出現
                appeared_times = 0
                for next_draw in next_draws_list:
                    if num in next_draw:
                        appeared_times += 1
                
                # 如果在每一期下一期都出現
                if appeared_times == len(next_draws_list):
                    numbers_appeared_every_time.append(num)
            
            if numbers_appeared_every_time:
                must_appear_combinations_3plus.append({
                    'quadruplet': quadruplet,
                    'occurrences': len(indices_with_quadruplet),
                    'must_appear_numbers': sorted(numbers_appeared_every_time),
                    'next_draws_list': next_draws_list
                })
    
    if must_appear_combinations_3plus:
        print(f"\n找到 {len(must_appear_combinations_3plus)} 個出現3次或以上的模式:")
        print("="*80)
        
        # 按數字組合的字典順序排序（從小到大）
        must_appear_combinations_3plus.sort(key=lambda x: x['quadruplet'])
        
        for i, combo in enumerate(must_appear_combinations_3plus, 1):
            quadruplet = combo['quadruplet']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"{i}. 數字組合 {quadruplet[0]}, {quadruplet[1]}, {quadruplet[2]}, {quadruplet[3]}:")
            print(f"   出現 {occurrences} 次")
            print(f"   下一期每次都出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            for j, next_draw in enumerate(combo['next_draws_list']):
                print(f"     第{j+1}次後的下一期: {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 60)
    else:
        print(f"\n在 {len(frequent_quadruplets_3plus)} 個出現3次或以上的四個數字組合中，")
        print("也沒有找到下一期一定出現特定數字的模式")

if __name__ == "__main__":
    find_all_four_number_must_appear()