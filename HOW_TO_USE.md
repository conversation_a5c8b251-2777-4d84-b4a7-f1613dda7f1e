# 號碼比對程式使用說明

## 功能介紹
此程式可比對您輸入的六個號碼與 'data_compare_lines1.csv' 檔案中的號碼，檢查是否有完全一樣的號碼組合或其他匹配情況。

## 使用方法

### 方法一：命令列參數方式
```bash
python compare_numbers.py "13,21,23,27,31,49"
```

### 方法二：互動式輸入
```bash
python compare_numbers.py
```
程式會提示您輸入六個號碼，例如：`1,2,3,4,5,6`

## 輸出說明

### 完全匹配
- 如果輸入的六個號碼與 CSV 檔案中的某一行完全相同，程式會顯示：
  ```
  發現完全一樣的號碼組合! 位於第 X 行
  該行號碼: [號碼列表]
  ```

### 部分匹配
- 顯示與輸入號碼有部分重複的行：
  ```
  第 Y 行: 重複號碼 [重複的號碼] (共 Z 個)
  該行號碼: [該行完整的六個號碼]
  ```

### 統計資訊
- 顯示每個輸入號碼在整個 CSV 檔案中的出現次數

## 特色功能
- 快速檢查是否有完全一樣的號碼組合
- 顯示所有部分匹配的資料
- 提供詳細的統計分析
- 支援互動式和命令列兩種輸入方式

## 注意事項
- 輸入必須是六個不重複的正整數
- 號碼以逗號分隔
- 程式會比對 1 到 49 的號碼範圍（根據 CSV 檔案內容）