import csv
import json
from collections import Counter

def read_last_n_lines(filename, n=7):
    """讀取CSV檔案的最後n行"""
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        last_lines = lines[-n:]  # 取最後n行
        
    # 解析CSV數據
    data = []
    for line in last_lines:
        numbers = [int(x.strip()) for x in line.strip().split(',')]
        data.append(numbers)
    
    return data

def load_results_json(filename):
    """載入results.json檔案"""
    with open(filename, 'r', encoding='utf-8') as file:
        data = json.load(file)
    return data

def analyze_and_predict(last_lines_data, results_data):
    """分析數據並預估可能出現的號碼"""
    print("最後七筆數據:")
    for i, line in enumerate(last_lines_data):
        print(f"第{len(last_lines_data)-6+i}筆: {line}")
    
    print("\n分析結果:")
    
    # 統計最後七筆數據中各號碼出現的頻率
    all_numbers = []
    for line in last_lines_data:
        all_numbers.extend(line)
    
    number_counts = Counter(all_numbers)
    print(f"最後七筆數據中各號碼出現次數: {dict(number_counts)}")
    
    # 從results.json中獲取每個位置的高頻號碼
    predictions = {}
    for position, numbers_list in results_data.items():
        predictions[position] = [item['number'] for item in numbers_list[:5]]  # 取前5個高頻號碼
    
    print("\n根據results.json的高頻號碼預測:")
    for pos, nums in predictions.items():
        print(f"第{pos}個位置可能的號碼: {nums}")
    
    # 找出在最後七筆數據中出現過，且在results.json中也為高頻的號碼
    print("\n綜合預測 - 同時符合最後數據和高頻統計的號碼:")
    combined_predictions = {}
    for pos, nums in predictions.items():
        # 找出同時在最後數據和高頻號碼中的號碼
        last_data_set = set(all_numbers)
        high_freq_set = set(nums)
        common_numbers = last_data_set.intersection(high_freq_set)
        combined_predictions[pos] = sorted(list(common_numbers))
        
        if combined_predictions[pos]:
            print(f"第{pos}個位置建議號碼: {combined_predictions[pos]}")
        else:
            print(f"第{pos}個位置建議號碼: 無重疊號碼 (使用高頻號碼) {nums[:3]}")
    
    return combined_predictions

def main():
    # 讀取最後七筆數據
    last_lines_data = read_last_n_lines('data_compare_lines1.csv', 7)
    
    # 讀取results.json
    results_data = load_results_json('reverse_tracking_project/results.json')
    
    # 分析並預估
    predictions = analyze_and_predict(last_lines_data, results_data)
    
    print("\n最終建議選號組合:")
    # 提供幾種可能的組合
    print("組合1 (取各位置第一個建議):")
    combination1 = []
    for pos in sorted(predictions.keys()):
        if predictions[pos]:
            combination1.append(predictions[pos][0] if predictions[pos] else results_data[pos][0]['number'])
        else:
            combination1.append(results_data[pos][0]['number'])
    print(combination1)
    
    print("組合2 (隨機取各位置建議):")
    import random
    combination2 = []
    for pos in sorted(predictions.keys()):
        if predictions[pos]:
            choice = random.choice(predictions[pos]) if predictions[pos] else results_data[pos][0]['number']
        else:
            choice = results_data[pos][0]['number']
        combination2.append(choice)
    print(combination2)
    
    print("\n基於整體高頻率的建議號碼:")
    # 統計整體高頻號碼
    all_frequent_numbers = set()
    for pos in results_data:
        for item in results_data[pos][:3]:  # 每個位置前3個高頻號碼
            all_frequent_numbers.add(item['number'])
    
    print(f"各位置高頻號碼 (前3): {sorted(list(all_frequent_numbers))}")

if __name__ == "__main__":
    main()