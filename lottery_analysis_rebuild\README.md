# 彩券分析工具

一個功能完整的彩券號碼分析工具，包含多種分析模式和直觀的使用者介面。

## 功能特色

- **模組化設計**：清晰的程式碼結構，易於維護和擴充
- **高效能**：使用pandas進行高效數據處理
- **完整分析**：支援號碼比較、頻率分析、分布分析等
- **視覺化**：生成圖表展示分析結果
- **錯誤處理**：完善的異常處理機制
- **多種介面**：提供選單、CLI、程式碼介面

## 檔案結構

```
lottery_analysis_rebuild/
├── __init__.py              # 套件初始化
├── config.py               # 配置設定
├── data_loader.py          # 資料載入模組
├── analyzer.py             # 基本分析器
├── advanced_analyzer.py    # 進階分析器
├── menu_launcher.py        # 選單啟動器
├── cli_tool.py             # CLI工具
├── main.py                 # 主程式
├── run_analysis.py         # 執行分析腳本
├── launch.bat              # Windows啟動批次檔
├── requirements.txt        # 依賴套件
├── README.md               # 說明文件
├── QUICK_START.md          # 快速開始指南
├── CLI_USAGE.md            # CLI使用說明
├── START_MENU.md           # 選單使用說明
├── demo_menu.py            # 功能演示
├── test_menu.py            # 測試腳本
├── example_usage.py        # 使用範例
├── setup.py                # 安裝設定
└── data_compare_lines1.csv # 範例數據檔案
```

## 安裝依賴

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：啟動選單（推薦）

```bash
python menu_launcher.py
```

或使用Windows批次檔：
```bash
launch.bat
```

選單提供以下功能：
1. 號碼比較 - 比較您的號碼與資料庫
2. 進階分析 - 全面分析資料庫趨勢
3. 視覺化圖表 - 生成數據圖表
4. 號碼統計 - 查看特定號碼統計
5. 批次比較 - 一次比較多組號碼
6. 互動模式 - 輕鬆互動式分析

### 方法2：CLI工具

```bash
# 比較號碼
python cli_tool.py compare --numbers "1,2,3,4,5,6,7,8"

# 進階分析
python cli_tool.py analyze

# 生成視覺化圖表
python cli_tool.py visualize --output "chart.png"

# 號碼統計
python cli_tool.py stats --numbers "1,2,3,4,5,6,7,8"

# 批次比較
python cli_tool.py batch --numbers-list "1,2,3,4,5,6,7,8;9,10,11,12,13,14,15,16"
```

### 方法3：程式碼介面

```python
from analyzer import LotteryAnalyzer
from advanced_analyzer import AdvancedLotteryAnalyzer

# 基本分析
analyzer = LotteryAnalyzer('data_compare_lines1.csv')
results = analyzer.compare_numbers([1,2,3,4,5,6,7,8])

# 進階分析
advanced_analyzer = AdvancedLotteryAnalyzer('data_compare_lines1.csv')
report = advanced_analyzer.generate_detailed_report()
```

## 配置選項

- `DEFAULT_CSV_FILE`: 預設CSV檔案路徑
- `DEFAULT_NUMBERS_COUNT`: 預設號碼數量 (8)
- `LOTTERY_NUMBER_MIN/MAX`: 號碼範圍 (1-49)
- `DEFAULT_MIN_MATCH_COUNT`: 最小匹配數量 (3)
- `MAX_DISPLAY_COUNT`: 每類結果最大顯示數量 (10)

## 支援的檔案格式

CSV檔案應包含以逗號分隔的數字，每行代表一組彩券號碼，例如：
```
1,2,3,4,5,6
7,8,9,10,11,12
13,14,15,16,17,18
```

## 系統要求

- Python 3.7 或更高版本
- Windows、macOS 或 Linux 系統

## 版本資訊

- 版本：1.0.0
- 更新日期：2025年11月