import csv
from collections import Counter
import os

def analyze_number_4_11_pair():
    """
    Analyze the last digits that appear after numbers 4 and 11 appear together in the same draw
    """
    # Check if file exists
    if not os.path.exists('data_compare_lines1.csv'):
        print("Error: data_compare_lines1.csv file not found")
        return
    
    # Read CSV file
    try:
        with open('data_compare_lines1.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("Error: data_compare_lines1.csv file not found")
        return
    except UnicodeDecodeError:
        # If UTF-8 decoding fails, try other encoding
        try:
            with open('data_compare_lines1.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("Error: Unable to decode the encoding of data_compare_lines1.csv file")
            return
    
    # Parse data
    draws = []
    for line in lines:
        # Remove line numbers and newlines, extract numbers
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # Find indices where both numbers 4 and 11 appear
    indices_with_4_11 = []
    for i, draw in enumerate(draws):
        if 4 in draw and 11 in draw:
            indices_with_4_11.append(i)
    
    print(f"Numbers 4 and 11 appeared together in the following draws: {[idx+1 for idx in indices_with_4_11]}")
    print(f"Total occurrences: {len(indices_with_4_11)}")
    
    # Collect all numbers from the next draws and analyze last digits
    next_draw_numbers = []
    for idx in indices_with_4_11:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
            print(f"Draw {idx+1}({draws[idx]}) -> Draw {idx+2}({next_draw})")
    
    if next_draw_numbers:
        print(f"\nAfter 4 and 11 appear together, the next draw has {len(next_draw_numbers)} numbers total")
        
        # Analyze last digits of next draw numbers
        last_digits = [num % 10 for num in next_draw_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\nLast digit occurrences in next draw numbers:")
        for digit in sorted(last_digit_counts.keys()):
            print(f" Last digit {digit}: {last_digit_counts[digit]} times")
        
        # Find most common last digit
        most_common_digit = last_digit_counts.most_common(1)[0]
        print(f"\nMost common last digit after 4 and 11 appear together: Last digit {most_common_digit[0]} ({most_common_digit[1]} times)")
        
        # Show top 3 last digits
        print(f"\nTop 3 last digits:")
        top_3_digits = last_digit_counts.most_common(3)
        for i, (digit, count) in enumerate(top_3_digits, 1):
            percentage = (count / len(last_digits)) * 10
            print(f" {i}. Last digit {digit}: {count} times ({percentage:.2f}%)")
        
        # Show which numbers have this last digit
        numbers_with_most_common_digit = [num for num in next_draw_numbers if num % 10 == most_common_digit[0]]
        number_counts = Counter(numbers_with_most_common_digit)
        print(f"\nNumbers with last digit {most_common_digit[0]} and their counts:")
        for num, count in sorted(number_counts.items()):
            print(f" Number {num}: {count} times")
    
    # Compare with occurrences of only 4 or only 11
    print(f"\n=== Comparison Analysis ===")
    
    # Find draws where only 4 appears (not 11)
    indices_with_4_only = [i for i in range(len(draws)) if 4 in draws[i] and 1 not in draws[i]]
    
    # Find draws where only 11 appears (not 4)
    indices_with_11_only = [i for i in range(len(draws)) if 11 in draws[i] and 4 not in draws[i]]
    
    # Analyze next draw digits when only 4 appears
    if indices_with_4_only:
        next_4_only_numbers = []
        for idx in indices_with_4_only:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_4_only_numbers.extend(next_draw)
        
        if next_4_only_numbers:
            last_digits_4_only = [num % 10 for num in next_4_only_numbers]
            last_digit_counts_4_only = Counter(last_digits_4_only)
            
            print(f"\nWhen only 4 appears (not 11), next draw last digit distribution:")
            top_3_4_only = last_digit_counts_4_only.most_common(3)
            for i, (digit, count) in enumerate(top_3_4_only, 1):
                percentage = (count / len(last_digits_4_only)) * 100
                print(f" {i}. Last digit {digit}: {count} times ({percentage:.2f}%)")
    
    # Analyze next draw digits when only 11 appears
    if indices_with_11_only:
        next_11_only_numbers = []
        for idx in indices_with_11_only:
            if idx + 1 < len(draws):
                next_draw = draws[idx + 1]
                next_11_only_numbers.extend(next_draw)
        
        if next_11_only_numbers:
            last_digits_11_only = [num % 10 for num in next_11_only_numbers]
            last_digit_counts_11_only = Counter(last_digits_11_only)
            
            print(f"\nWhen only 11 appears (not 4), next draw last digit distribution:")
            top_3_11_only = last_digit_counts_11_only.most_common(3)
            for i, (digit, count) in enumerate(top_3_11_only, 1):
                percentage = (count / len(last_digits_11_only)) * 10
                print(f" {i}. Last digit {digit}: {count} times ({percentage:.2f}%)")
    
    # Analyze when 4 and 1 appear together, the influence of other numbers in the same draw on next draw last digits
    if indices_with_4_11:
        if next_draw_numbers:  # Ensure there's data for analysis
            print(f"\n=== When 4 and 11 appear together, influence of other numbers in the same draw on next draw last digit {most_common_digit[0]} ===")
            
            paired_numbers_stats = {}
            
            for idx in indices_with_4_11:
                if idx + 1 < len(draws):
                    current_draw = draws[idx]  # Current draw numbers (including 4 and 11)
                    next_draw = draws[idx + 1]  # Next draw numbers
                    
                    # Find other numbers in the current draw besides 4 and 11
                    other_numbers = [num for num in current_draw if num != 4 and num != 11]
                    
                    # Check if the next draw contains the most common last digit
                    next_draw_digits = [n % 10 for n in next_draw]
                    has_most_common_digit = most_common_digit[0] in next_draw_digits
                    
                    for num in other_numbers:
                        if num not in paired_numbers_stats:
                            paired_numbers_stats[num] = {'total': 0, 'with_most_common_digit': 0}
                        
                        paired_numbers_stats[num]['total'] += 1
                        if has_most_common_digit:
                            paired_numbers_stats[num]['with_most_common_digit'] += 1
            
            # Filter numbers that appear less than 2 times
            significant_pairs = {num: stats for num, stats in paired_numbers_stats.items() if stats['total'] >= 2}
            
            if significant_pairs:
                print(f"Numbers that appear with 4,11 at least 2 times and influence on next draw last digit {most_common_digit[0]}:")
                print("(Format: Number: [total occurrences, occurrences with that last digit, success rate])")
                
                sorted_pairs = sorted(significant_pairs.items(), 
                                     key=lambda x: x[1]['with_most_common_digit']/x[1]['total'] if x[1]['total'] > 0 else 0, 
                                     reverse=True)
                
                for num, stats in sorted_pairs:
                    success_rate = (stats['with_most_common_digit'] / stats['total']) * 100
                    print(f"  Number {num:2d}: [{stats['total']}, {stats['with_most_common_digit']}, {success_rate:.2f}%]")
                
                # Show numbers with highest success rate
                print(f"\nNumbers that have the highest probability of producing last digit {most_common_digit[0]} when paired with 4,11:")
                top_pairs = [(num, stats) for num, stats in sorted_pairs if stats['with_most_common_digit'] > 0]
                for num, stats in top_pairs[:10]:  # Show top 10
                    success_rate = (stats['with_most_common_digit'] / stats['total']) * 100
                    print(f" Number {num:2d}: Probability {success_rate:.2f}% (Total {stats['total']} times, Success {stats['with_most_common_digit']} times)")
            else:
                print("Not enough data for triple number analysis")

if __name__ == "__main__":
    analyze_number_4_11_pair()