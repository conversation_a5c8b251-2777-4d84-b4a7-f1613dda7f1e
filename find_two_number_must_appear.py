import csv
from collections import Counter
import os

def find_two_number_must_appear():
    """
    尋找兩個數字組合出現後，下一期一定會出現的數字
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("尋找兩個數字組合後下一期一定會出現的數字...")
    print("="*70)
    
    # 測試所有可能的兩個數字組合
    must_appear_combinations = []
    
    # 限制搜索範圍以提高效率
    for first_num in range(1, 30):
        for second_num in range(first_num+1, 35):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 只考慮至少出現3次的組合以確保統計意義
            if len(indices_with_pair) >= 3:
                # 獲取下一期的所有數字
                next_draws_list = []
                
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draws_list.append(next_draw)
                
                if next_draws_list:
                    # 檢查是否有數字在每一次下一期都出現
                    all_possible_numbers = set()
                    for next_draw in next_draws_list:
                        all_possible_numbers.update(next_draw)
                    
                    numbers_appeared_every_time = []
                    for num in all_possible_numbers:
                        # 計算這個數字在多少期下一期中出現
                        appeared_times = 0
                        for next_draw in next_draws_list:
                            if num in next_draw:
                                appeared_times += 1
                        
                        # 如果在每一期下一期都出現
                        if appeared_times == len(next_draws_list):
                            numbers_appeared_every_time.append(num)
                    
                    if numbers_appeared_every_time:
                        must_appear_combinations.append({
                            'pair': (first_num, second_num),
                            'occurrences': len(indices_with_pair),
                            'must_appear_numbers': sorted(numbers_appeared_every_time),
                            'next_draws_list': next_draws_list
                        })
    
    # 輸出結果
    if must_appear_combinations:
        for combo in must_appear_combinations:
            pair = combo['pair']
            occurrences = combo['occurrences']
            must_appear_numbers = combo['must_appear_numbers']
            
            print(f"數字組合 {pair[0]}, {pair[1]}:")
            print(f" 出現 {occurrences} 次")
            print(f" 下一期一定會出現的數字: {must_appear_numbers}")
            
            # 顯示詳細信息
            print("  詳細情況:")
            for i, next_draw in enumerate(combo['next_draws_list']):
                print(f"    第{i+1}次: 下一期號碼 {sorted(next_draw)} -> 包含 {must_appear_numbers}")
            print("-" * 50)
    else:
        print("沒有找到兩個數字組合出現後，下一期一定會出現特定數字的模式")
        print("(至少需要3次出現才有統計意義)")
    
    # 也檢查出現2次的情況（雖然統計意義較弱）
    print("\n尋找出現2次的組合中一定會出現的數字...")
    print("="*70)
    
    two_occurrence_combinations = []
    
    for first_num in range(1, 30):
        for second_num in range(first_num+1, 35):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 檢查出現2次的情況
            if len(indices_with_pair) == 2:
                # 獲取下一期的所有數字
                next_draws_list = []
                
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draws_list.append(next_draw)
                
                if next_draws_list and len(next_draws_list) == 2:
                    # 檢查是否有數字在兩次下一期中都出現
                    first_draw_set = set(next_draws_list[0])
                    second_draw_set = set(next_draws_list[1])
                    
                    # 找交集
                    common_numbers = first_draw_set.intersection(second_draw_set)
                    
                    if common_numbers:
                        two_occurrence_combinations.append({
                            'pair': (first_num, second_num),
                            'occurrences': 2,
                            'common_numbers': sorted(common_numbers),
                            'next_draws_list': next_draws_list
                        })
    
    # 輸出出現2次的結果
    if two_occurrence_combinations:
        count = 0
        for combo in two_occurrence_combinations[:20]:  # 只顯示前20個
            pair = combo['pair']
            occurrences = combo['occurrences']
            common_numbers = combo['common_numbers']
            
            print(f"數字組合 {pair[0]}, {pair[1]}:")
            print(f" 出現 {occurrences} 次")
            print(f" 兩次下一期都出現的數字: {common_numbers}")
            
            # 顯示詳細信息
            for i, next_draw in enumerate(combo['next_draws_list']):
                print(f"    第{i+1}次: 下一期號碼 {sorted(next_draw)}")
            print("-" * 50)
            count += 1
        
        if count == 20:
            print(f"... 還有更多結果")
    else:
        print("沒有找到出現2次的兩個數字組合中，下一期都出現相同數字的模式")

if __name__ == "__main__":
    find_two_number_must_appear()