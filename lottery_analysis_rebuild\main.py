"""彩券號碼比較主程式"""

import sys
from typing import List
from .config import Config
from .analyzer import LotteryAnalyzer


def get_user_input() -> List[int]:
    """獲取使用者輸入"""
    while True:
        try:
            input_str = input("請輸入八個號碼 (以逗號分隔，例如: 1,2,3,4,5,6,7,8): ")
            input_numbers = [int(num.strip()) for num in input_str.split(',')]
            
            if len(input_numbers) != Config.DEFAULT_NUMBERS_COUNT:
                print(f"請輸入剛好{Config.DEFAULT_NUMBERS_COUNT}個號碼!")
                continue
            
            if len(set(input_numbers)) != Config.DEFAULT_NUMBERS_COUNT:
                print(f"請輸入{Config.DEFAULT_NUMBERS_COUNT}個不重複的號碼!")
                continue
                
            return input_numbers
        except ValueError:
            print("請輸入有效的數字，以逗號分隔!")


def display_results(analyzer: LotteryAnalyzer, input_numbers: List[int], results: dict):
    """顯示分析結果"""
    # 尋找完全匹配
    exact_match_line = analyzer.find_exact_match(input_numbers)
    if exact_match_line:
        print(f"發現完全一樣的號碼組合! 位於第 {exact_match_line} 行")
        print(f"該行號碼: {sorted(list(analyzer.data[exact_match_line-1]))}")
        print()
    else:
        print("沒有找到完全一樣的號碼組合")
        print()
    
    # 顯示部分匹配結果
    total_matches = sum(len(matches) for matches in results.values())
    if total_matches > 0:
        print(f"找到 {total_matches} 筆部分匹配的資料:")
        
        for count in range(7, Config.DEFAULT_MIN_MATCH_COUNT - 1, -1):  # 7 down to min_match_count
            matches = results[count]
            if matches:
                print(f"\n有 {len(matches)} 筆資料包含 {count} 個重複號碼:")
                for match in matches[:Config.MAX_DISPLAY_COUNT]:  # 顯示前N筆
                    print(f"  第 {match.line} 行: 重複號碼 {match.numbers} / 該行號碼: {match.original_row}")
                if len(matches) > Config.MAX_DISPLAY_COUNT:
                    print(f"  ... 還有 {len(matches) - Config.MAX_DISPLAY_COUNT} 筆資料")
        print()
    else:
        print("沒有找到任何部分匹配的資料")
    
    # 顯示統計
    print("-" * 50)
    print("統計資訊:")
    
    number_counts = analyzer.get_number_statistics(input_numbers)
    
    print("輸入號碼在CSV中的出現次數:")
    for num in sorted(input_numbers):
        print(f"  號碼 {num:2d}: {number_counts[num]} 次")
    
    # 各重複數量的匹配筆數
    print("\n各重複數量的匹配筆數:")
    for count in range(7, Config.DEFAULT_MIN_MATCH_COUNT - 1, -1):  # 7 to min_match_count
        matches_count = len(results[count])
        if matches_count > 0:
            print(f" {count} 個重複號碼: {matches_count} 筆")


def main():
    """主函數"""
    print("Number comparison program")
    print("Comparing with 'data_compare_lines1.csv' file")
    print("-" * 50)
    
    try:
        analyzer = LotteryAnalyzer()
        print(f"Successfully loaded {len(analyzer.data)} lines of data")
    except FileNotFoundError:
        print("Error: 'data_compare_lines1.csv' file not found")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # 獲取輸入號碼
    if len(sys.argv) > 1:
        try:
            input_numbers = [int(num.strip()) for num in sys.argv[1].split(',')]
        except ValueError:
            print("Please provide valid numbers separated by commas as argument")
            return
    else:
        input_numbers = get_user_input()
    
    print(f"輸入號碼: {sorted(input_numbers)}")
    print("-" * 50)
    
    # 執行比較
    results = analyzer.compare_numbers(input_numbers)
    
    # 顯示結果
    display_results(analyzer, input_numbers, results)


if __name__ == "__main__":
    main()