import csv
from collections import Counter

def analyze_number_24_44_simple():
    """
    簡化版分析：當數字24和44同時出現在同一期時，下一期各尾數的出現次數
    """
    # 讀取CSV文件
    with open('data_compare_lines1.csv', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字24和44同時出現的行的索引
    indices_with_24_44 = []
    for i, draw in enumerate(draws):
        if 24 in draw and 44 in draw:
            indices_with_24_44.append(i)
    
    print(f"數字24和44同時在以下期數出現: {[idx+1 for idx in indices_with_24_44]}")
    print(f"總共出現 {len(indices_with_24_44)} 次")
    
    if not indices_with_24_44:
        print("沒有找到24和44同時出現的期數")
        return
    
    # 收集這些期數下一期的所有數字，並分析尾數
    next_draw_numbers = []
    for idx in indices_with_24_44:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
            print(f"第 {idx+1} 期({draws[idx]}) -> 第 {idx+2} 期({next_draw})")
    
    if next_draw_numbers:
        print(f"\n當24和44同時出現後，下一期總共出現 {len(next_draw_numbers)} 個號碼")
        
        # 分析下一期號碼的尾數
        last_digits = [num % 10 for num in next_draw_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\n下一期號碼的尾數出現次數統計:")
        for digit in sorted(last_digit_counts.keys()):
            print(f" 尾數 {digit}: {last_digit_counts[digit]} 次")
        
        # 找出出現最多的尾數
        most_common_digit = last_digit_counts.most_common(1)[0]
        print(f"\n當24和44同時出現後，下一期出現最多的尾數是: 尾數 {most_common_digit[0]} (出現 {most_common_digit[1]} 次)")
        
        # 顯示前3名尾數
        print(f"\n前3名尾數:")
        top_3_digits = last_digit_counts.most_common(3)
        for i, (digit, count) in enumerate(top_3_digits, 1):
            percentage = (count / len(last_digits)) * 100
            print(f" {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")
        
        # 顯示該尾數包含哪些號碼
        numbers_with_most_common_digit = [num for num in next_draw_numbers if num % 10 == most_common_digit[0]]
        number_counts = Counter(numbers_with_most_common_digit)
        print(f"\n尾數 {most_common_digit[0]} 包含的號碼及其次數:")
        for num, count in sorted(number_counts.items()):
            print(f"  數字 {num}: {count} 次")

if __name__ == "__main__":
    analyze_number_24_44_simple()
