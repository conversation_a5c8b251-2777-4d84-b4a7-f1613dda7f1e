import csv
import json
from io import StringIO
from itertools import combinations
from collections import Counter, defaultdict


def parse_csv_data(csv_content):
    """
    Parses CSV content into a list of lists of integers.
    Each inner list represents a line of numbers.
    """
    data = []
    # Use StringIO to treat the string content as a file
    csvfile = StringIO(csv_content)
    reader = csv.reader(csvfile)
    for row in reader:
        # Convert each number string to an integer
        data.append([int(num) for num in row])
    return data


def analyze_reverse_tracking_49_numbers(all_draws, lookback_period=2):
    """
    分析 1 到 49 號碼的反向追蹤 - 前N期內最常同時出現三個的號碼或以上
    :param all_draws: 所有開獎結果
    :param lookback_period: 回顧期數，預設為2期
    :return: 包含分析結果的字典
    """
    results = {}
    
    if len(all_draws) > lookback_period:
        # 計算1到49每個號碼在歷史中出現後，前N期內最常同時出現三個或以上的號碼組合
        number_cooccurrence = defaultdict(lambda: Counter())
        for num in range(1, 50):
            for idx, draw in enumerate(all_draws[lookback_period:], lookback_period):  # 從第(lookback_period+1)期開始，確保前N期存在
                if num in draw:
                    # 統計前N期內出現的號碼組合（三個或以上）
                    for prev_idx in range(idx-lookback_period, idx):
                        prev_draw = all_draws[prev_idx]
                        # 找出前N期中三個或以上號碼的組合
                        if len(prev_draw) >= 3:  # 確保前一期至少有3個號碼
                            for combo in combinations(prev_draw, 3):
                                # 將組合轉為元組以便計數
                                combo_tuple = tuple(sorted(combo))
                                number_cooccurrence[num][combo_tuple] += 1
        
        # 將每個號碼在前N期內最常出現的三個號碼組合記錄到結果字典中
        for num in range(1, 50):
            if num in number_cooccurrence:
                top_5 = number_cooccurrence[num].most_common(10)
                results[num] = []
                for assoc_combo, count in top_5:
                    total_occurrences = sum(number_cooccurrence[num].values())
                    percentage = (count / total_occurrences) * 100 if total_occurrences > 0 else 0
                    results[num].append({
                        'combination': list(assoc_combo),
                        'count': count,
                        'percentage': round(percentage, 2)  # 提高精度到小數點後2位
                    })
            else:
                results[num] = [] # 該號碼在歷史中未出現過
    
    return results


def print_reverse_tracking_results(results, lookback_period=5):
    """
    打印反向追蹤分析結果
    :param results: 分析結果字典
    :param lookback_period: 回顧期數
    """
    print(f"\n=== 1到49號碼反向追蹤分析 - 前{lookback_period}期內最常同時出現三個的號碼或以上 ===")
    
    for num in range(1, 50):
        if results[num]:  # 如果該號碼有記錄
            print(f"\n號碼 {num} 出現前{lookback_period}期內最常同時出現的三個號碼組合:")
            for item in results[num]:
                print(f" 組合 {item['combination']}: {item['count']} 次 ({item['percentage']}%)")
        else:
            print(f"\n號碼 {num} 在歷史中未出現過")


def save_results_to_json(results, filename='results.json'):
    """
    將結果保存到JSON文件
    :param results: 分析結果字典
    :param filename: 輸出文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n結果已保存到 {filename}")


def load_results_from_json(filename='results.json'):
    """
    從JSON文件加載結果
    :param filename: 輸入文件名
    :return: 分析結果字典
    """
    with open(filename, 'r', encoding='utf-8') as f:
        results = json.load(f)
    # 將字串鍵轉換回整數鍵
    results = {int(k): v for k, v in results.items()}
    return results


def get_top_numbers_for_draw(results, draw_numbers):
    """
    根據給定的開獎號碼，獲取對應的前N期最常出現號碼
    :param results: 分析結果字典
    :param draw_numbers: 開獎號碼列表
    :return: 每個號碼對應的前N期最常出現號碼
    """
    draw_analysis = {}
    for num in draw_numbers:
        if num in results and results[num]:
            draw_analysis[num] = results[num]
    return draw_analysis


def main():
    try:
        with open('../data_compare_lines1.csv', 'r') as f:
            csv_content = f.read()
    except FileNotFoundError:
        # 如果在子目錄中找不到，嘗試直接在當前目錄查找
        try:
            with open('data_compare_lines1.csv', 'r') as f:
                csv_content = f.read()
        except FileNotFoundError:
            print("Error: data_compare_lines1.csv not found in current or parent directory.")
            return

    all_draws = parse_csv_data(csv_content)
    
    # 執行反向追蹤分析
    results = analyze_reverse_tracking_49_numbers(all_draws, lookback_period=2)
    
    # 打印結果
    print_reverse_tracking_results(results, lookback_period=2)
    
    # 顯示結果字典的結構
    print(f"\n分析完成！結果已記錄在字典中，包含 {len(results)} 個號碼的追蹤資料")
    
    # 保存結果到JSON文件
    save_results_to_json(results, filename='reverse_tracking_project/results.json')
    
    # 顯示最後一期開獎號碼的分析
    if all_draws:
        last_draw = all_draws[-1]
        print(f"\n最後一期開獎號碼: {sorted(last_draw)}")
        last_draw_analysis = get_top_numbers_for_draw(results, last_draw)
        print("這些號碼在出現前2期內最常同時出現三個的號碼組合:")
        for num, top_combinations in last_draw_analysis.items():
            print(f" 號碼 {num}: {top_combinations[:10]}")  # 只顯示前3個


if __name__ == "__main__":
    main()