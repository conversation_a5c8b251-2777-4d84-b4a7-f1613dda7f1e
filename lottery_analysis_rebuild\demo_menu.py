#!/usr/bin/env python3
"""啟動選單功能演示"""

import sys
from pathlib import Path

# 添加項目路徑到系統路徑
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from config import Config
from analyzer import LotteryAnalyzer
from advanced_analyzer import AdvancedLotteryAnalyzer


def demo_functionality():
    """演示啟動選單中的功能"""
    print("啟動選單功能演示")
    print("=" * 50)
    
    # 創建分析器
    analyzer = LotteryAnalyzer(Config.DEFAULT_CSV_FILE)
    print(f"O 成功載入 {len(analyzer.data)} 行數據")
    
    # 演示選項1: 號碼比較
    print("\n演示選項1: 號碼比較")
    test_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
    print(f"比較號碼: {test_numbers}")
    
    results = analyzer.compare_numbers(test_numbers)
    total_matches = sum(len(matches) for matches in results.values())
    print(f"找到 {total_matches} 筆匹配資料")
    
    for count in range(7, 2, -1):
        matches = results.get(count, [])
        if matches:
            print(f" {count} 個重複號碼: {len(matches)} 筆")
    
    # 演示選項2: 進階分析
    print("\n演示選項2: 進階分析")
    advanced_analyzer = AdvancedLotteryAnalyzer(Config.DEFAULT_CSV_FILE)
    report = advanced_analyzer.generate_detailed_report()
    
    print(f"總行數: {report['total_rows']}")
    print(f"唯一數字數: {report['unique_numbers']}")
    print("出現頻率最高的5個數字:")
    for num, freq in report['top_numbers'][:5]:
        print(f" 號碼 {num}: {freq} 次")
    
    # 演示選項3: 號碼統計
    print("\n演示選項4: 號碼統計")
    stats = analyzer.get_number_statistics(test_numbers)
    print("各號碼出現次數:")
    for num in sorted(test_numbers):
        print(f" 號碼 {num:2d}: {stats[num]} 次")
    
    print("\n啟動選單包含以下功能:")
    print("1. 號碼比較 - 比較您的號碼與資料庫")
    print("2. 進階分析 - 全面分析資料庫趨勢") 
    print("3. 視覺化圖表 - 生成數據圖表")
    print("4. 號碼統計 - 查看特定號碼統計")
    print("5. 批次比較 - 一次比較多組號碼")
    print("6. 互動模式 - 輕鬆互動式分析")
    print("0. 離開")
    
    print("\n所有功能都已準備就緒，您可以執行以下命令啟動選單:")
    print("python menu_launcher.py")


if __name__ == "__main__":
    demo_functionality()