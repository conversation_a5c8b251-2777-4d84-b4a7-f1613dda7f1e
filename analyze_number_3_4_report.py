import csv
from collections import Counter
import os

def analyze_number_3_4_report():
    """
    專門分析當3和4同時出現時，統計最常出現的尾數
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果 UTF-8 解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解讀 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # 找到數字3和4同時出現的行的索引
    indices_with_3_4 = []
    for i, draw in enumerate(draws):
        if 3 in draw and 4 in draw:
            indices_with_3_4.append(i)
    
    print(f"當3和4同時出現的期數: {[idx+1 for idx in indices_with_3_4]}")
    print(f"總共出現次數: {len(indices_with_3_4)}")
    
    # 收集這些期數下一期的所有數字，並分析尾數
    next_draw_numbers = []
    for idx in indices_with_3_4:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
    
    if next_draw_numbers:
        print(f"\n當3和4同時出現後，下一期總共出現 {len(next_draw_numbers)} 個號碼")
        
        # 分析下一期號碼的尾數
        last_digits = [num % 10 for num in next_draw_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\n下一期號碼的尾數出現次數統計:")
        for digit in sorted(last_digit_counts.keys()):
            print(f"  尾數 {digit}: {last_digit_counts[digit]} 次")
        
        # 找出出現最多的尾數
        most_common_digit = last_digit_counts.most_common(1)[0]
        print(f"\n當3和4同時出現後，下一期出現最多的尾數是: 尾數 {most_common_digit[0]} (出現 {most_common_digit[1]} 次)")
        
        # 顯示前10名尾數
        print(f"\n前10名尾數 (按出現次數排序):")
        all_digits_sorted = last_digit_counts.most_common()
        for i, (digit, count) in enumerate(all_digits_sorted, 1):
            percentage = (count / len(last_digits)) * 10
            print(f"  {i}. 尾數 {digit}: {count} 次 ({percentage:.2f}%)")
        
        # 顯示該尾數包含哪些號碼
        numbers_with_most_common_digit = [num for num in next_draw_numbers if num % 10 == most_common_digit[0]]
        number_counts = Counter(numbers_with_most_common_digit)
        print(f"\n出現最多次的尾數 {most_common_digit[0]} 包含的號碼及其次數:")
        for num, count in sorted(number_counts.items()):
            print(f" 號碼 {num}: {count} 次")

if __name__ == "__main__":
    analyze_number_3_4_report()