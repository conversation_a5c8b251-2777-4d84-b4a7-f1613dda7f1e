import csv
from collections import Counter
import os

def analyze_24_44_for_digit_7():
    """
    專門分析數字24、44配對時，下一期尾數7的出現情況
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("分析數字24、44配對時，下一期尾數7的出現情況")
    print("="*60)
    
    # 找出包含24、44的抽獎
    indices_with_24_44 = []
    for i, draw in enumerate(draws):
        if 24 in draw and 44 in draw:
            indices_with_24_44.append(i)
    
    print(f"數字 24、44 一起出現的總次數: {len(indices_with_24_44)}")
    
    if len(indices_with_24_44) == 0:
        print("在數據中沒有找到數字24、44同時出現的情況")
        return
    
    # 獲取24、44出現後的下一期抽獎數字
    next_draw_numbers = []
    next_draw_info = []  # 記錄每期的詳細信息
    
    for idx in indices_with_24_44:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
            next_draw_info.append({
                'draw_number': idx + 2,  # 下一期的期數
                'numbers': next_draw,
                'last_digits': [num % 10 for num in next_draw]
            })
    
    if not next_draw_numbers:
        print("沒有下一期的數據可用")
        return
    
    print(f"數字 24、44 一起出現後，下一期共有 {len(next_draw_numbers)} 個數字")
    
    # 分析尾數分布
    last_digits = [num % 10 for num in next_draw_numbers]
    last_digit_counts = Counter(last_digits)
    
    print(f"\n下一期數字的尾數分布:")
    print("-" * 30)
    for digit in sorted(last_digit_counts.keys()):
        count = last_digit_counts[digit]
        percentage = (count / len(last_digits)) * 100
        print(f"  尾數 {digit}: {count} 次 ({percentage:.2f}%)")
    
    # 特別分析尾數7
    digit_7_count = last_digit_counts.get(7, 0)
    digit_7_percentage = (digit_7_count / len(last_digits)) * 100
    print(f"\n尾數 7 的詳細情況:")
    print(f"  出現次數: {digit_7_count}")
    print(f" 出現機率: {digit_7_percentage:.2f}%")
    
    # 檢查每一期的具體情況
    print(f"\n每次 24, 44 出現後的下一期詳細:")
    for info in next_draw_info:
        has_digit_7 = 7 in info['last_digits']
        digit_7_count_in_draw = info['last_digits'].count(7)
        print(f" 期數 {info['draw_number']:3d}: {sorted(info['numbers'])} -> 尾數 {sorted(info['last_digits'])} -> 尾數7出現{digit_7_count_in_draw}次")
    
    # 分析添加不同第三個數字對尾數7機率的影響
    print(f"\n分析添加不同第三個數字對尾數7機率的影響:")
    print("-" * 60)
    
    third_number_results = []
    
    for third_number in range(1, 50):
        if third_number == 24 or third_number == 44:
            continue
            
        # 找出包含24、44和第三個數字的抽獎
        indices_with_24_44_third = []
        for i, draw in enumerate(draws):
            if 24 in draw and 44 in draw and third_number in draw:
                indices_with_24_44_third.append(i)
        
        if len(indices_with_24_44_third) >= 2:  # 至少2次才有統計意義
            # 獲取下一期的數字
            next_draw_numbers_third = []
            for idx in indices_with_24_44_third:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers_third.extend(next_draw)
            
            if next_draw_numbers_third:
                last_digits_third = [num % 10 for num in next_draw_numbers_third]
                last_digit_counts_third = Counter(last_digits_third)
                
                digit_7_count_third = last_digit_counts_third.get(7, 0)
                digit_7_percentage_third = (digit_7_count_third / len(last_digits_third)) * 100 if len(last_digits_third) > 0 else 0
                
                third_number_results.append({
                    'third_number': third_number,
                    'occurrences': len(indices_with_24_44_third),
                    'digit_7_count': digit_7_count_third,
                    'digit_7_percentage': digit_7_percentage_third,
                    'total_numbers': len(last_digits_third)
                })
    
    # 按尾數7機率排序
    third_number_results.sort(key=lambda x: x['digit_7_percentage'], reverse=True)
    
    print(f"添加第三個數字後尾數7機率排名 (至少2次出現):")
    print(f"{'排名':<4} {'第三個數字':<6} {'出現次數':<6} {'尾數7次數':<6} {'尾數7機率':<10} {'總數字數':<6}")
    print("-" * 60)
    
    for i, result in enumerate(third_number_results[:20], 1):
        print(f"{i:<4} {result['third_number']:<6} {result['occurrences']:<6} {result['digit_7_count']:<6} {result['digit_7_percentage']:<10.2f}% {result['total_numbers']:<6}")

if __name__ == "__main__":
    analyze_24_44_for_digit_7()