import csv
from collections import Counter
import os

def analyze_high_probability_combinations():
    """
    分析高機率出現特定尾數的數字組合
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("分析高機率出現特定尾數的數字組合...")
    print("="*60)
    
    # 測試一些特定的數字組合（如您提到的24,44）
    test_combinations = [
        (24, 44),  # 您提到的組合
        (5, 8),    # 我們剛分析的組合
        (3, 4),    # 基礎分析的組合
    ]
    
    for first_num, second_num in test_combinations:
        # 找出同時包含first_num和second_num的抽獎
        indices_with_pair = []
        for i, draw in enumerate(draws):
            if first_num in draw and second_num in draw:
                indices_with_pair.append(i)
        
        print(f"\n數字 {first_num}, {second_num} 組合分析:")
        print(f"  出現次數: {len(indices_with_pair)}")
        
        if len(indices_with_pair) >= 2:
            # 獲取下一期的數字
            next_draw_numbers = []
            for idx in indices_with_pair:
                if idx + 1 < len(draws):
                    next_draw = draws[idx + 1]
                    next_draw_numbers.extend(next_draw)
            
            if next_draw_numbers:
                # 分析尾數
                last_digits = [num % 10 for num in next_draw_numbers]
                last_digit_counts = Counter(last_digits)
                
                print(f" 下一期總數字數: {len(last_digits)}")
                print(" 各尾數出現統計:")
                
                # 計算各尾數的機率
                digit_stats = []
                for digit in range(0, 10):
                    count = last_digit_counts.get(digit, 0)
                    if count > 0:
                        percentage = (count / len(last_digits)) * 100
                        digit_stats.append((digit, count, percentage))
                
                # 按機率排序
                digit_stats.sort(key=lambda x: x[2], reverse=True)
                
                for digit, count, percentage in digit_stats:
                    print(f"    尾數 {digit}: {count} 次 ({percentage:.2f}%)")
                
                # 顯示最高機率的尾數
                if digit_stats:
                    top_digit = digit_stats[0]
                    print(f" 最高機率尾數: 尾數 {top_digit[0]} ({top_digit[2]:.2f}%)")
                    
                    # 如果最高機率超過50%，特別標註
                    if top_digit[2] > 50:
                        print(f" *** 高機率模式: 尾數 {top_digit[0]} 出現機率 > 50% ***")
    
    print("\n" + "="*60)
    print("尋找所有高機率組合 (至少3次出現，最高尾數機率 > 20%)")
    print("="*60)
    
    high_prob_combinations = []
    
    # 測試一些常見的數字組合
    for first_num in range(1, 20):
        for second_num in range(first_num+1, 25):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 只考慮至少出現3次的組合
            if len(indices_with_pair) >= 3:
                # 獲取下一期的數字
                next_draw_numbers = []
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draw_numbers.extend(next_draw)
                
                if next_draw_numbers:
                    # 分析尾數
                    last_digits = [num % 10 for num in next_draw_numbers]
                    last_digit_counts = Counter(last_digits)
                    
                    # 計算最高機率尾數
                    max_prob = 0
                    max_digit = -1
                    for digit in range(0, 10):
                        count = last_digit_counts.get(digit, 0)
                        if count > 0:
                            percentage = (count / len(last_digits)) * 10
                            if percentage > max_prob:
                                max_prob = percentage
                                max_digit = digit
                    
                    if max_prob > 20:  # 超過20%機率的組合
                        high_prob_combinations.append((first_num, second_num, max_digit, max_prob, len(indices_with_pair), len(last_digits)))
    
    # 排序並顯示高機率組合
    high_prob_combinations.sort(key=lambda x: x[3], reverse=True)
    
    print(f"找到 {len(high_prob_combinations)} 個高機率組合:")
    for i, (first, second, digit, prob, occurrences, total_nums) in enumerate(high_prob_combinations[:20], 1):
        print(f"{i:2d}. 數字 {first:2d}, {second:2d} → 尾數 {digit} ({prob:.2f}%, 出現 {occurrences} 次, {total_nums} 個數字)")

if __name__ == "__main__":
    analyze_high_probability_combinations()