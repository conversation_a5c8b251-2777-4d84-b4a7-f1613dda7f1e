from itertools import combinations

def generate_combinations(numbers, size=6):
    """從十個數字中生成六個數字的所有組合"""
    return list(combinations(numbers, size))

def display_combinations(combinations_list):
    """顯示所有六個數字的組合"""
    print(f"從十個數字生成的所有六個數字組合：")
    print("="*40)
    for i, combo in enumerate(combinations_list, 1):
        print(f"{i:3d}. {combo}")
    print("="*40)
    print(f"總共生成了 {len(combinations_list)} 組六個數字的組合")

def main():
    # 使用預設的十個數字
    user_numbers = [1, 5, 12, 18, 23, 27, 31, 35, 42, 48]
    print(f"使用的十個數字為: {user_numbers}")
    print()
    
    # 生成六個數字的組合
    print("正在生成六個數字的所有組合...")
    six_number_combinations = generate_combinations(user_numbers, 6)
    
    # 顯示結果
    display_combinations(six_number_combinations)

if __name__ == "__main__":
    main()