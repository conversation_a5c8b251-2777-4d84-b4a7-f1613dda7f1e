import csv
from collections import Counter
import os

def find_must_appear_numbers():
    """
    尋找在特定數字組合出現後，下一期一定會出現的數字
    """
    # 檢查文件是否存在
    if not os.path.exists('data_compare_lines2.csv'):
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    
    # 讀取CSV文件
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("錯誤: 找不到 data_compare_lines2.csv 文件")
        return
    except UnicodeDecodeError:
        # 如果UTF-8解碼失敗，嘗試其他編碼
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("錯誤: 無法解碼 data_compare_lines2.csv 文件的編碼")
            return
    
    # 解析數據
    draws = []
    for line in lines:
        # 移除行號和換行符，提取數字
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    print("尋找特定組合後下一期一定會出現的數字...")
    print("="*70)
    
    # 測試不同的數字組合，看是否有數字在下一期一定會出現
    must_appear_combinations = []
    
    # 測試一些出現次數較多的數字組合
    for first_num in range(1, 20):
        for second_num in range(first_num+1, 25):
            # 找出同時包含first_num和second_num的抽獎
            indices_with_pair = []
            for i, draw in enumerate(draws):
                if first_num in draw and second_num in draw:
                    indices_with_pair.append(i)
            
            # 只考慮至少出現3次的組合以確保統計意義
            if len(indices_with_pair) >= 3:
                # 獲取下一期的所有數字
                next_draw_all_numbers = []
                next_draws_list = []
                
                for idx in indices_with_pair:
                    if idx + 1 < len(draws):
                        next_draw = draws[idx + 1]
                        next_draw_all_numbers.extend(next_draw)
                        next_draws_list.append(next_draw)
                
                if next_draw_all_numbers and next_draws_list:
                    # 計算每個數字在下一期出現的頻率
                    number_counts = Counter(next_draw_all_numbers)
                    
                    # 檢查是否有數字在每一次下一期都出現
                    numbers_appeared_every_time = []
                    
                    # 檢查每個在下一期出現過的數字
                    for num in set(next_draw_all_numbers):
                        # 計算這個數字在多少期下一期中出現
                        appeared_times = 0
                        for next_draw in next_draws_list:
                            if num in next_draw:
                                appeared_times += 1
                        
                        # 如果在每一期下一期都出現
                        if appeared_times == len(next_draws_list):
                            numbers_appeared_every_time.append(num)
                    
                    if numbers_appeared_every_time:
                        print(f"數字組合 {first_num}, {second_num}:")
                        print(f"  出現 {len(indices_with_pair)} 次")
                        print(f" 下一期一定會出現的數字: {sorted(numbers_appeared_every_time)}")
                        print(f"  這些數字在所有 {len(next_draws_list)} 次下一期中都出現了")
                        print("-" * 50)
    
    print("\n尋找三個數字組合後下一期一定會出現的數字...")
    print("="*70)
    
    # 測試三個數字的組合
    for first_num in range(1, 15):
        for second_num in range(first_num+1, 20):
            for third_num in range(second_num+1, 25):
                # 找出同時包含三個數字的抽獎
                indices_with_triplet = []
                for i, draw in enumerate(draws):
                    if first_num in draw and second_num in draw and third_num in draw:
                        indices_with_triplet.append(i)
                
                # 只考慮至少出現2次的組合
                if len(indices_with_triplet) >= 2:
                    # 獲取下一期的所有數字
                    next_draws_list = []
                    
                    for idx in indices_with_triplet:
                        if idx + 1 < len(draws):
                            next_draw = draws[idx + 1]
                            next_draws_list.append(next_draw)
                    
                    if next_draws_list:
                        # 檢查是否有數字在每一次下一期都出現
                        all_possible_numbers = set()
                        for next_draw in next_draws_list:
                            all_possible_numbers.update(next_draw)
                        
                        numbers_appeared_every_time = []
                        for num in all_possible_numbers:
                            # 計算這個數字在多少期下一期中出現
                            appeared_times = 0
                            for next_draw in next_draws_list:
                                if num in next_draw:
                                    appeared_times += 1
                            
                            # 如果在每一期下一期都出現
                            if appeared_times == len(next_draws_list):
                                numbers_appeared_every_time.append(num)
                        
                        if numbers_appeared_every_time:
                            print(f"數字組合 {first_num}, {second_num}, {third_num}:")
                            print(f"  出現 {len(indices_with_triplet)} 次")
                            print(f" 下一期一定會出現的數字: {sorted(numbers_appeared_every_time)}")
                            print(f"  這些數字在所有 {len(next_draws_list)} 次下一期中都出現了")
                            print("-" * 50)

if __name__ == "__main__":
    find_must_appear_numbers()