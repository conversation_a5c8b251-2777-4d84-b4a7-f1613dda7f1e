import csv
from collections import Counter
import matplotlib.pyplot as plt
import numpy as np

# 讀取CSV檔案
def read_csv_data(filename):
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, start=1):
            line = line.strip()
            if line:
                numbers = [int(num) for num in line.split(',')]
                if len(numbers) == 6:  # 確保每行有6個數字
                    data.append(numbers)
                else:
                    print(f"警告: 第{line_num}行的數字數量不是6個: {numbers}")
    return data

# 分析數字出現頻率
def analyze_frequency(data):
    all_numbers = [num for row in data for num in row]
    frequency_counter = Counter(all_numbers)
    return frequency_counter

# 分析數字在各位置的分布
def analyze_position_distribution(data):
    position_counts = [Counter() for _ in range(6)]  # 6個位置
    
    for row in data:
        for pos, num in enumerate(row):
            position_counts[pos][num] += 1
    
    return position_counts

# 分析數字範圍分布
def analyze_range_distribution(data):
    ranges = {
        '1-10': 0,
        '11-20': 0,
        '21-30': 0,
        '31-40': 0,
        '41-49': 0
    }
    
    all_numbers = [num for row in data for num in row]
    
    for num in all_numbers:
        if 1 <= num <= 10:
            ranges['1-10'] += 1
        elif 11 <= num <= 20:
            ranges['11-20'] += 1
        elif 21 <= num <= 30:
            ranges['21-30'] += 1
        elif 31 <= num <= 40:
            ranges['31-40'] += 1
        elif 41 <= num <= 49:
            ranges['41-49'] += 1
    
    return ranges

# 分析數字和值的分布
def analyze_sum_distribution(data):
    sums = [sum(row) for row in data]
    return sums

# 分析奇偶數分布
def analyze_odd_even_distribution(data):
    odd_even_stats = []
    
    for row in data:
        odd_count = sum(1 for num in row if num % 2 == 1)
        even_count = 6 - odd_count
        odd_even_stats.append((odd_count, even_count))
    
    return odd_even_stats

# 主要分析函數
def main():
    filename = 'data_compare_lines1.csv'
    print(f"開始分析檔案: {filename}")
    
    # 讀取數據
    data = read_csv_data(filename)
    print(f"總共讀取了 {len(data)} 行數據")
    
    # 1. 分析數字出現頻率
    print("\n=== 數字出現頻率分析 ===")
    frequency_counter = analyze_frequency(data)
    sorted_freq = sorted(frequency_counter.items(), key=lambda x: x[1], reverse=True)
    
    print("出現頻率最高的10個數字:")
    for num, freq in sorted_freq[:10]:
        print(f"數字 {num}: 出現 {freq} 次")
    
    print("\n出現頻率最低的10個數字:")
    for num, freq in sorted_freq[-10:]:
        print(f"數字 {num}: 出現 {freq} 次")
    
    # 2. 分析各位置數字分布
    print("\n=== 各位置數字分布分析 ===")
    position_counts = analyze_position_distribution(data)
    
    for pos in range(6):
        print(f"第{pos+1}位置最常出現的數字:")
        pos_sorted = sorted(position_counts[pos].items(), key=lambda x: x[1], reverse=True)[:5]
        for num, freq in pos_sorted:
            print(f"  數字 {num}: 出現 {freq} 次")
    
    # 3. 分析範圍分布
    print("\n=== 數字範圍分布 ===")
    range_dist = analyze_range_distribution(data)
    total_numbers = sum(range_dist.values())
    for range_name, count in range_dist.items():
        percentage = (count / total_numbers) * 10
        print(f"{range_name}: {count} 個數字 ({percentage:.2f}%)")
    
    # 4. 分析和值分布
    print("\n=== 數字和值分布 ===")
    sums = analyze_sum_distribution(data)
    print(f"最小和值: {min(sums)}")
    print(f"最大和值: {max(sums)}")
    print(f"平均和值: {sum(sums) / len(sums):.2f}")
    
    # 5. 分析奇偶數分布
    print("\n=== 奇偶數分布 ===")
    odd_even_stats = analyze_odd_even_distribution(data)
    
    odd_even_counter = Counter(odd_even_stats)
    for (odd, even), count in odd_even_counter.most_common():
        print(f"奇數{odd}個/偶數{even}個: 出現 {count} 次")
    
    # 6. 生成統計摘要
    print("\n=== 統計摘要 ===")
    print(f"總行數: {len(data)}")
    print(f"總數字數: {len(data) * 6}")
    print(f"唯一數字數: {len(frequency_counter)}")
    print(f"數字範圍: {min(frequency_counter.keys())} - {max(frequency_counter.keys())}")
    
    # 生成圖表
    # 頻率分布圖
    numbers, frequencies = zip(*sorted_freq)
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 2, 1)
    plt.bar(numbers, frequencies)
    plt.title('數字出現頻率分布')
    plt.xlabel('數字')
    plt.ylabel('出現次數')
    
    # 範圍分布圖
    plt.subplot(2, 2, 2)
    range_names = list(range_dist.keys())
    range_counts = list(range_dist.values())
    plt.bar(range_names, range_counts)
    plt.title('數字範圍分布')
    plt.xlabel('範圍')
    plt.ylabel('數字總數')
    
    # 和值分布圖
    plt.subplot(2, 2, 3)
    plt.hist(sums, bins=30, edgecolor='black')
    plt.title('和值分布')
    plt.xlabel('和值')
    plt.ylabel('出現次數')
    
    # 奇偶數分布圖
    plt.subplot(2, 2, 4)
    odd_counts = [odd for odd, even in odd_even_stats]
    plt.hist(odd_counts, bins=range(8), edgecolor='black', align='left')
    plt.title('每行奇數數量分布')
    plt.xlabel('奇數數量')
    plt.ylabel('出現次數')
    plt.xticks(range(7))
    
    plt.tight_layout()
    plt.savefig('lottery_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return {
        'data': data,
        'frequency': frequency_counter,
        'position_distribution': position_counts,
        'range_distribution': range_dist,
        'sums': sums,
        'odd_even_stats': odd_even_stats
    }

if __name__ == "__main__":
    results = main()