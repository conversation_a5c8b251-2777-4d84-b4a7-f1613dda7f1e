import csv
from collections import Counter
import os

def analyze_number_3_4_7_report():
    """
    Analyze the last digits that appear after numbers 3, 4 and 7 appear together in the same draw
    """
    # Check if file exists
    if not os.path.exists('data_compare_lines2.csv'):
        print("Error: data_compare_lines2.csv file not found")
        return
    
    # Read CSV file
    try:
        with open('data_compare_lines2.csv', 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except FileNotFoundError:
        print("Error: data_compare_lines2.csv file not found")
        return
    except UnicodeDecodeError:
        # If UTF-8 decoding fails, try other encoding
        try:
            with open('data_compare_lines2.csv', 'r', encoding='big5') as file:
                lines = file.readlines()
        except UnicodeDecodeError:
            print("Error: Unable to decode the encoding of data_compare_lines2.csv file")
            return
    
    # Parse data
    draws = []
    for line in lines:
        # Remove line numbers and newlines, extract numbers
        line_content = line.strip()
        if ',' in line_content:
            numbers = [int(num) for num in line_content.split(',')]
            draws.append(numbers)
    
    # Find indices where numbers 3, 4 and 7 all appear
    indices_with_3_4_7 = []
    for i, draw in enumerate(draws):
        if 3 in draw and 4 in draw and 7 in draw:
            indices_with_3_4_7.append(i)
    
    print(f"Numbers 3, 4 and 7 appeared together in the following draws: {[idx+1 for idx in indices_with_3_4_7]}")
    print(f"Total occurrences: {len(indices_with_3_4_7)}")
    
    # Collect all numbers from the next draws and analyze last digits
    next_draw_numbers = []
    for idx in indices_with_3_4_7:
        if idx + 1 < len(draws):
            next_draw = draws[idx + 1]
            next_draw_numbers.extend(next_draw)
    
    if next_draw_numbers:
        print(f"\nAfter 3, 4 and 7 appear together, the next draw has {len(next_draw_numbers)} numbers total")
        
        # Analyze last digits of next draw numbers
        last_digits = [num % 10 for num in next_draw_numbers]
        last_digit_counts = Counter(last_digits)
        
        print(f"\nLast digit occurrences in next draw numbers:")
        for digit in sorted(last_digit_counts.keys()):
            print(f" Last digit {digit}: {last_digit_counts[digit]} times")
        
        # Find most common last digit
        most_common_digit = last_digit_counts.most_common(1)[0]
        print(f"\nMost common last digit after 3, 4 and 7 appear together: Last digit {most_common_digit[0]} ({most_common_digit[1]} times)")
        
        # Show top 10 last digits
        print(f"\nTop 10 last digits (sorted by occurrence):")
        all_digits_sorted = last_digit_counts.most_common()
        for i, (digit, count) in enumerate(all_digits_sorted, 1):
            percentage = (count / len(last_digits)) * 10
            print(f"  {i}. Last digit {digit}: {count} times ({percentage:.2f}%)")
        
        # Show which numbers have this last digit
        numbers_with_most_common_digit = [num for num in next_draw_numbers if num % 10 == most_common_digit[0]]
        number_counts = Counter(numbers_with_most_common_digit)
        print(f"\nNumbers with the most common last digit {most_common_digit[0]} and their counts:")
        for num, count in sorted(number_counts.items()):
            print(f" Number {num}: {count} times")
    else:
        print(f"\nAfter 3, 4 and 7 appear together, there are no next draws to analyze (no more data or all at end of dataset)")

if __name__ == "__main__":
    analyze_number_3_4_7_report()