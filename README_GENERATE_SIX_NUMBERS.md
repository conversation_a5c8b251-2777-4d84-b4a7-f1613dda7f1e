# 六個號碼組合生成器

## 概述
這個程式專門用於從十個數字中生成所有可能的六個數字組合，每組包含六個號碼。

## 功能
- 從用戶輸入的十個數字中生成所有可能的六個數字組合
- 輸出所有生成的組合
- 總共會生成 C(10,6) = 210 組不同的六個數字組合

## 程式檔案
- `generate_six_number_combinations.py` - 互動式版本，讓用戶輸入十個數字
- `test_six_number_combinations.py` - 測試版本，使用預設數字展示功能

## 使用方式

### 互動式版本
```bash
python generate_six_number_combinations.py
```

### 測試版本
```bash
python test_six_number_combinations.py
```

## 範例輸出
使用數字 [1, 5, 12, 18, 23, 27, 31, 35, 42, 48] 會生成 210 組六個數字的組合，例如：
- (1, 5, 12, 18, 23, 27)
- (1, 5, 12, 18, 23, 31)
- (1, 5, 12, 18, 23, 35)
- ...
- (23, 27, 31, 35, 42, 48)

## 技術細節
- 使用 Python 的 `itertools.combinations` 函數生成組合
- 確保每組組合中的數字不重複
- 每組包含恰好六個數字